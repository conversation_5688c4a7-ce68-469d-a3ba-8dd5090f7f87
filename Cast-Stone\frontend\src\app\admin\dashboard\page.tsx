'use client';

import { useEffect, useState } from 'react';
import { useAdmin } from '../../../contexts/AdminContext';
import styles from './page.module.css';

interface DashboardStats {
  totalProducts: number;
  totalOrders: number;
  totalUsers: number;
  totalRevenue: number;
  recentOrders: any[];
  topProducts: any[];
}

export default function AdminDashboard() {
  const { state, logout, apiCall } = useAdmin();
  const { admin } = state;
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalRevenue: 0,
    recentOrders: [],
    topProducts: []
  });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await apiCall('/admin/analytics/dashboard');
        console.log('Dashboard API response:', response);

        if (response.success && response.data) {
          const data = response.data as any;
          setStats({
            totalProducts: data.summary?.totalProducts || 0,
            totalOrders: data.summary?.totalOrders || 0,
            totalUsers: data.summary?.totalUsers || 0,
            totalRevenue: data.summary?.totalRevenue || 0,
            recentOrders: data.recentOrders || [],
            topProducts: data.topProducts || []
          });
        } else {
          console.error('Failed to fetch dashboard data:', response.message);
          // Fallback to empty data
          setStats({
            totalProducts: 0,
            totalOrders: 0,
            totalUsers: 0,
            totalRevenue: 0,
            recentOrders: [],
            topProducts: []
          });
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        // Fallback to empty data
        setStats({
          totalProducts: 0,
          totalOrders: 0,
          totalUsers: 0,
          totalRevenue: 0,
          recentOrders: [],
          topProducts: []
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <div className={styles.loading}>
        <div className={styles.spinner}></div>
        <p>Loading dashboard...</p>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h1>Admin Dashboard</h1>
          <p>Welcome back, {admin?.name || 'Admin'}</p>
        </div>
        <button
          onClick={logout}
          className={styles.logoutButton}
        >
          Logout
        </button>
      </div>

      <div className={styles.content}>
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <h3>Products</h3>
            <p className={styles.statNumber}>{stats.totalProducts}</p>
          </div>
          <div className={styles.statCard}>
            <h3>Orders</h3>
            <p className={styles.statNumber}>{stats.totalOrders}</p>
          </div>
          <div className={styles.statCard}>
            <h3>Users</h3>
            <p className={styles.statNumber}>{stats.totalUsers}</p>
          </div>
          <div className={styles.statCard}>
            <h3>Revenue</h3>
            <p className={styles.statNumber}>${stats.totalRevenue.toFixed(2)}</p>
          </div>
        </div>

        <div className={styles.section}>
          <h2>Quick Actions</h2>
          <div className={styles.actionGrid}>
            <a href="/admin/products" className={styles.actionCard}>
              <h3>Manage Products</h3>
              <p>Add, edit, or remove products</p>
            </a>
            <a href="/admin/orders" className={styles.actionCard}>
              <h3>View Orders</h3>
              <p>Process and manage orders</p>
            </a>
            <a href="/admin/users" className={styles.actionCard}>
              <h3>User Management</h3>
              <p>View and manage users</p>
            </a>
            <a href="/admin/analytics" className={styles.actionCard}>
              <h3>Analytics</h3>
              <p>View sales and performance data</p>
            </a>
          </div>
        </div>

        {stats.recentOrders.length > 0 && (
          <div className={styles.section}>
            <h2>Recent Orders</h2>
            <div className={styles.ordersTable}>
              <table>
                <thead>
                  <tr>
                    <th>Order ID</th>
                    <th>Customer</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th>Date</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.recentOrders.slice(0, 5).map((order: any) => (
                    <tr key={order._id}>
                      <td>#{order._id.slice(-6)}</td>
                      <td>{order.customerInfo?.name || 'N/A'}</td>
                      <td>${order.totalAmount?.toFixed(2) || '0.00'}</td>
                      <td>
                        <span className={`${styles.status} ${styles[order.status?.toLowerCase() || 'pending']}`}>
                          {order.status || 'Pending'}
                        </span>
                      </td>
                      <td>{new Date(order.createdAt).toLocaleDateString()}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

