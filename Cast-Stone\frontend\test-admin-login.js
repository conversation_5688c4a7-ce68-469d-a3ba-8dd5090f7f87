/**
 * Frontend Admin Login Test
 * Tests the complete login flow from frontend to backend
 */

const puppeteer = require('puppeteer');

async function testAdminLogin() {
  let browser;
  
  try {
    console.log('🚀 Starting Frontend Admin Login Test');
    console.log('=====================================\n');
    
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false, // Set to true for headless mode
      defaultViewport: { width: 1280, height: 720 }
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('❌ Browser Error:', msg.text());
      }
    });
    
    // Navigate to admin login
    console.log('📍 Navigating to admin login page...');
    await page.goto('http://localhost:3000/admin/login', { 
      waitUntil: 'networkidle0' 
    });
    
    // Check if login form is present
    console.log('🔍 Checking login form...');
    await page.waitForSelector('form', { timeout: 5000 });
    
    const emailInput = await page.$('input[type="email"]');
    const passwordInput = await page.$('input[type="password"]');
    const submitButton = await page.$('button[type="submit"]');
    
    if (!emailInput || !passwordInput || !submitButton) {
      throw new Error('Login form elements not found');
    }
    
    console.log('✅ Login form found');
    
    // Test invalid credentials first
    console.log('🧪 Testing invalid credentials...');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', 'wrongpassword');
    await page.click('button[type="submit"]');
    
    // Wait for error message
    await page.waitForTimeout(2000);
    
    // Clear form
    await page.evaluate(() => {
      document.querySelector('input[type="email"]').value = '';
      document.querySelector('input[type="password"]').value = '';
    });
    
    // Test valid credentials
    console.log('🧪 Testing valid credentials...');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', '132Trent@!');
    
    // Submit form
    await page.click('button[type="submit"]');
    
    // Wait for navigation to dashboard
    console.log('⏳ Waiting for dashboard redirect...');
    await page.waitForNavigation({ 
      waitUntil: 'networkidle0',
      timeout: 10000 
    });
    
    // Check if we're on the dashboard
    const currentUrl = page.url();
    console.log('📍 Current URL:', currentUrl);
    
    if (currentUrl.includes('/admin/dashboard')) {
      console.log('✅ Successfully redirected to dashboard');
      
      // Check for dashboard elements
      await page.waitForSelector('h1', { timeout: 5000 });
      const title = await page.$eval('h1', el => el.textContent);
      
      if (title && title.includes('Dashboard')) {
        console.log('✅ Dashboard loaded successfully');
        console.log('🎉 Admin login test PASSED!');
        return true;
      } else {
        throw new Error('Dashboard title not found');
      }
    } else {
      throw new Error(`Expected dashboard URL, got: ${currentUrl}`);
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testAdminLogin().then(success => {
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test error:', error);
  process.exit(1);
});
