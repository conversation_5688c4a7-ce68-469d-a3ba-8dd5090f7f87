/**
 * Test Browser Login Flow
 * Simulates the exact browser login flow with debugging
 */

const puppeteer = require('puppeteer');

async function testBrowserLogin() {
  let browser;
  
  try {
    console.log('🚀 Testing Browser Login Flow with Debugging');
    console.log('==============================================\n');
    
    // Launch browser with debugging
    browser = await puppeteer.launch({ 
      headless: false,
      devtools: true, // Open DevTools
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable console logging from the page
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      
      if (type === 'log') {
        console.log(`📝 Console: ${text}`);
      } else if (type === 'error') {
        console.log(`❌ Error: ${text}`);
      } else if (type === 'warn') {
        console.log(`⚠️  Warning: ${text}`);
      }
    });
    
    // Catch page errors
    page.on('pageerror', error => {
      console.log(`💥 Page Error: ${error.message}`);
    });
    
    // Catch failed requests
    page.on('requestfailed', request => {
      console.log(`🚫 Request Failed: ${request.url()} - ${request.failure().errorText}`);
    });
    
    // Navigate to login page
    console.log('📍 Step 1: Navigate to login page');
    await page.goto('http://localhost:3000/admin/login', { 
      waitUntil: 'networkidle0',
      timeout: 15000
    });
    
    console.log('✅ Login page loaded');
    
    // Wait for form to be ready
    console.log('📍 Step 2: Wait for form elements');
    await page.waitForSelector('input[type="email"]', { timeout: 10000 });
    await page.waitForSelector('input[type="password"]', { timeout: 10000 });
    await page.waitForSelector('button[type="submit"]', { timeout: 10000 });
    
    console.log('✅ Form elements found');
    
    // Fill credentials
    console.log('📍 Step 3: Fill credentials');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', '132Trent@!');
    
    console.log('✅ Credentials filled');
    
    // Submit form
    console.log('📍 Step 4: Submit form');
    
    // Listen for navigation
    const navigationPromise = new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Navigation timeout after 20 seconds'));
      }, 20000);
      
      page.on('framenavigated', (frame) => {
        if (frame === page.mainFrame()) {
          clearTimeout(timeout);
          resolve(frame.url());
        }
      });
    });
    
    // Click submit
    await page.click('button[type="submit"]');
    
    console.log('⏳ Waiting for navigation...');
    
    try {
      const finalUrl = await navigationPromise;
      console.log('📍 Navigation completed to:', finalUrl);
      
      if (finalUrl.includes('/admin/dashboard')) {
        console.log('✅ Successfully redirected to dashboard!');
        
        // Wait for dashboard content
        await page.waitForSelector('h1', { timeout: 10000 });
        const title = await page.$eval('h1', el => el.textContent);
        console.log('📄 Dashboard title:', title);
        
        console.log('🎉 LOGIN FLOW TEST PASSED!');
        return true;
      } else {
        console.log('❌ Unexpected redirect to:', finalUrl);
        return false;
      }
    } catch (navError) {
      console.log('⚠️  Navigation timeout, checking current URL...');
      const currentUrl = page.url();
      console.log('📍 Current URL:', currentUrl);
      
      if (currentUrl.includes('/admin/dashboard')) {
        console.log('✅ Found dashboard URL (slow navigation)');
        return true;
      } else if (currentUrl.includes('/admin/login')) {
        console.log('❌ Still on login page');
        
        // Check for error messages
        try {
          const errors = await page.$$eval('[class*="error"], [class*="alert"]', 
            elements => elements.map(el => el.textContent).filter(text => text.trim())
          );
          if (errors.length > 0) {
            console.log('❌ Error messages found:', errors);
          }
        } catch (e) {
          console.log('No error messages found');
        }
        
        return false;
      } else {
        console.log('❌ Unexpected URL:', currentUrl);
        return false;
      }
    }
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  } finally {
    if (browser) {
      console.log('\n⏳ Keeping browser open for 10 seconds to inspect...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      await browser.close();
    }
  }
}

// Run the test
testBrowserLogin().then(success => {
  console.log('\n' + '='.repeat(50));
  console.log(success ? '🎉 TEST PASSED' : '❌ TEST FAILED');
  console.log('='.repeat(50));
  process.exit(success ? 0 : 1);
}).catch(error => {
  console.error('Test error:', error);
  process.exit(1);
});
