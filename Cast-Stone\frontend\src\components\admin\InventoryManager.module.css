.inventoryManager {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.headerLeft h1 {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: var(--text-primary);
}

.headerLeft p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 1rem;
}

.headerRight {
  display: flex;
  gap: 1rem;
}

.exportButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--admin-accent);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exportButton:hover {
  background: var(--admin-accent-dark);
  transform: translateY(-1px);
}

/* Summary Cards */
.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.summaryCard {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  transition: all 0.2s ease;
}

.summaryCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.cardIcon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: var(--admin-accent-light);
  color: var(--admin-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.cardContent h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
}

.cardContent p {
  margin: 0;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Filters */
.filtersSection {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.searchBar {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchIcon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

.searchInput {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 3rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  background: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--admin-accent);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

.filterSelect {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 0.9rem;
  min-width: 150px;
}

.filterSelect:focus {
  outline: none;
  border-color: var(--admin-accent);
}

/* Table */
.tableContainer {
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  margin-bottom: 2rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th {
  background: var(--bg-secondary);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  font-size: 0.9rem;
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid var(--border-light);
  vertical-align: top;
}

.tableRow:hover {
  background: var(--bg-secondary);
}

/* Product Info */
.productInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.productDetails {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.productTitle {
  font-weight: 500;
  color: var(--text-primary);
}

.productSku {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-family: monospace;
}

/* Stock Numbers */
.stockNumber {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.reservedStock {
  font-weight: 500;
  color: var(--warning-dark);
}

.reorderPoint {
  font-weight: 500;
  color: var(--text-secondary);
}

/* Status */
.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  font-size: 0.9rem;
}

.statusInStock {
  color: var(--success-color);
}

.statusLowStock {
  color: var(--warning-color);
}

.statusOutOfStock {
  color: var(--error-color);
}

.statusDiscontinued {
  color: var(--text-muted);
}

/* Locations */
.locations {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.location {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.location svg {
  color: var(--text-muted);
}

/* Actions */
.actions {
  display: flex;
  gap: 0.5rem;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--text-secondary);
}

.actionButton:hover {
  border-color: var(--admin-accent);
  color: var(--admin-accent);
  background: var(--primary-color-5);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 2rem;
}

.modalContent {
  background: var(--bg-primary);
  border-radius: 12px;
  max-width: 500px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.modalHeader h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.closeButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-secondary);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.closeButton:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.modalBody {
  padding: 1.5rem;
}

.currentStock {
  background: var(--bg-secondary);
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
}

.currentStock p {
  margin: 0;
  color: var(--text-primary);
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup:last-child {
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.input,
.select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: border-color 0.2s ease;
}

.input:focus,
.select:focus {
  outline: none;
  border-color: var(--admin-accent);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid var(--border-color);
}

.cancelButton,
.saveButton {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancelButton {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancelButton:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.saveButton {
  background: var(--admin-accent);
  color: white;
}

.saveButton:hover {
  background: var(--admin-accent-dark);
  transform: translateY(-1px);
}

/* Loading and Empty State */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--admin-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
}

.emptyState svg {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.emptyState h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-primary);
}

.emptyState p {
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .inventoryManager {
    padding: 1rem;
  }
  
  .header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .summaryCards {
    grid-template-columns: 1fr;
  }
  
  .filtersSection {
    flex-direction: column;
    align-items: stretch;
  }
  
  .table {
    font-size: 0.9rem;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
  
  .productInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .modalActions {
    flex-direction: column;
  }
  
  .modalActions button {
    width: 100%;
  }
}
