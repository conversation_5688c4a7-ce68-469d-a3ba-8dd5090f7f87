/*!***********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/admin/CollectionHierarchy.module.css ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************/
.CollectionHierarchy_hierarchyContainer__lz8mn {
  background: white;
  border-radius: 8px;
  border: 1px solid var(--cart-border);
  overflow: hidden;
}

.CollectionHierarchy_hierarchyHeader__Ekcmd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  background: var(--background-light);
}

.CollectionHierarchy_hierarchyHeader__Ekcmd h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
}

.CollectionHierarchy_addRootButton__LUKk2 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.CollectionHierarchy_addRootButton__LUKk2:hover {
  background: var(--cart-primary-hover);
}

.CollectionHierarchy_hierarchyTree__IWInT {
  max-height: 600px;
  overflow-y: auto;
}

.CollectionHierarchy_collectionNode__J_Ag_ {
  border-bottom: 1px solid var(--cart-border);
}

.CollectionHierarchy_collectionNode__J_Ag_:last-child {
  border-bottom: none;
}

.CollectionHierarchy_collectionItem__3nwho {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 60px;
}

.CollectionHierarchy_collectionItem__3nwho:hover {
  background: var(--background-light);
}

.CollectionHierarchy_collectionItem__3nwho.CollectionHierarchy_selected__ExALV {
  background: var(--secondary-color);
  border-left: 3px solid var(--primary-color);
}

.CollectionHierarchy_collectionContent__ItDfW {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.CollectionHierarchy_expandButton__Tcc5i {
  width: 20px;
  display: flex;
  justify-content: center;
}

.CollectionHierarchy_expandToggle__bXKWi {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.CollectionHierarchy_expandToggle__bXKWi:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.CollectionHierarchy_expandSpacer__ueF5Q {
  width: 20px;
}

.CollectionHierarchy_collectionIcon__4adtE {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.CollectionHierarchy_collectionInfo__6mUL4 {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.CollectionHierarchy_collectionTitle__Id281 {
  font-weight: 500;
  color: var(--color-gray-900);
  font-size: 14px;
}

.CollectionHierarchy_collectionMeta__sxym6 {
  font-size: 12px;
  color: var(--color-gray-600);
}

.CollectionHierarchy_collectionStatus__unblF {
  margin-right: 12px;
}

.CollectionHierarchy_statusBadge__EWdjc {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.CollectionHierarchy_statusBadge__EWdjc.CollectionHierarchy_published__JPipe {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.CollectionHierarchy_statusBadge__EWdjc.CollectionHierarchy_draft__sMVxV {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.CollectionHierarchy_collectionActions__3bLBq {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-right: 12px;
}

.CollectionHierarchy_collectionItem__3nwho:hover .CollectionHierarchy_collectionActions__3bLBq {
  opacity: 1;
}

.CollectionHierarchy_actionButton__ZLATT {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.CollectionHierarchy_actionButton__ZLATT:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.CollectionHierarchy_childrenContainer__uTTfw {
  border-left: 1px solid var(--color-border-light);
  margin-left: 20px;
}

.CollectionHierarchy_loadingContainer__qlNq2 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.CollectionHierarchy_spinner__6xDfJ {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: CollectionHierarchy_spin__dSZzR 1s linear infinite;
}

@keyframes CollectionHierarchy_spin__dSZzR {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.CollectionHierarchy_errorContainer__PRqOl {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.CollectionHierarchy_errorMessage__RgoGB {
  color: var(--color-error);
  text-align: center;
  margin: 0;
}

.CollectionHierarchy_retryButton__jTYdl {
  padding: 8px 16px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.CollectionHierarchy_retryButton__jTYdl:hover {
  background: var(--color-primary-dark);
}

.CollectionHierarchy_emptyState__0W_fU {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  gap: 16px;
  color: var(--color-gray-600);
}

.CollectionHierarchy_emptyState__0W_fU svg {
  color: var(--color-gray-400);
}

.CollectionHierarchy_emptyState__0W_fU p {
  margin: 0;
  font-size: 16px;
  color: var(--color-gray-600);
}

.CollectionHierarchy_addFirstButton__4gxx9 {
  padding: 12px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.CollectionHierarchy_addFirstButton__4gxx9:hover {
  background: var(--color-primary-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .CollectionHierarchy_hierarchyHeader__Ekcmd {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .CollectionHierarchy_collectionItem__3nwho {
    padding: 8px 0;
    min-height: 50px;
  }
  
  .CollectionHierarchy_collectionActions__3bLBq {
    opacity: 1;
  }
  
  .CollectionHierarchy_actionButton__ZLATT {
    width: 32px;
    height: 32px;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/components/admin/CollectionModal.module.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
.CollectionModal_modalOverlay___vawn {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
  -webkit-backdrop-filter: blur(6px);
          backdrop-filter: blur(6px);
}

.CollectionModal_modalContainer__4GZZw {
  background: white;
  border-radius: 16px;
  width: 100%;
  max-width: 1200px;
  min-height: 600px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: CollectionModal_modalSlideIn__xKlOn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes CollectionModal_modalSlideIn__xKlOn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.CollectionModal_modalHeader__24erq {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 2px solid #e5e7eb;
  background: #ffffff;
  color: #1a1a1a;
}

.CollectionModal_modalHeader__24erq h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
}

.CollectionModal_closeButton__10tQk {
  background: #f3f4f6;
  border: 2px solid #d1d5db;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  color: #374151;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
}

.CollectionModal_closeButton__10tQk:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
  color: #1f2937;
  transform: scale(1.05);
}

.CollectionModal_modalForm__1KZOQ {
  padding: 40px;
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

.CollectionModal_formGrid__3mmYQ {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.CollectionModal_formSection__We35p {
  display: flex;
  flex-direction: column;
  gap: 28px;
  background: white;
  padding: 40px;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.CollectionModal_formSection__We35p h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  padding-bottom: 12px;
  border-bottom: 2px solid #d1d5db;
  position: relative;
}

.CollectionModal_formSection__We35p h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: #6b7280;
  border-radius: 1px;
}

.CollectionModal_formGroup__Joi79 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.CollectionModal_formGroup__Joi79 label {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.CollectionModal_formGroup__Joi79 input,
.CollectionModal_formGroup__Joi79 select,
.CollectionModal_formGroup__Joi79 textarea {
  padding: 14px 16px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 15px;
  background: white;
  color: #1f2937;
  transition: all 0.2s ease;
  font-family: inherit;
}

.CollectionModal_formGroup__Joi79 input:focus,
.CollectionModal_formGroup__Joi79 select:focus,
.CollectionModal_formGroup__Joi79 textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px var(--color-primary-light);
  transform: translateY(-1px);
}

.CollectionModal_formGroup__Joi79 textarea {
  resize: vertical;
  min-height: 80px;
}

.CollectionModal_formGroup__Joi79 small {
  font-size: 13px;
  color: var(--color-gray-600);
  line-height: 1.4;
  margin-top: 2px;
}

.CollectionModal_inputError__NVeC6 {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 4px var(--color-error-light) !important;
  background: var(--color-error-light) !important;
}

.CollectionModal_errorText__4rreW {
  font-size: 13px;
  color: var(--color-error);
  margin-top: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.CollectionModal_checkboxLabel__ENzz8 {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.CollectionModal_checkboxLabel__ENzz8 input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.CollectionModal_checkboxLabel__ENzz8 span {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.CollectionModal_uploadArea__XG9aV {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-gray-600);
}

.CollectionModal_uploadArea__XG9aV:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.CollectionModal_uploadArea__XG9aV p {
  margin: 8px 0 4px 0;
  font-weight: 500;
}

.CollectionModal_uploadArea__XG9aV small {
  color: var(--color-gray-500);
}

.CollectionModal_submitError__Gco32 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--color-error-light);
  color: var(--color-error-dark);
  border-radius: 6px;
  font-size: 14px;
  margin-top: 20px;
}

.CollectionModal_modalActions__qPhNr {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.CollectionModal_cancelButton__GjBkU {
  padding: 12px 24px;
  background: white;
  color: #374151;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.CollectionModal_cancelButton__GjBkU:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

.CollectionModal_saveButton__4Tx_q {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.CollectionModal_saveButton__4Tx_q:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.CollectionModal_saveButton__4Tx_q:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #9ca3af;
  border-color: #9ca3af;
}

/* Metafields Styling */
.CollectionModal_metafieldsContainer__2dkhR {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.CollectionModal_metafieldRow__oh_Jn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: var(--color-gray-50);
  border-radius: 12px;
  border: 2px solid var(--color-border);
  transition: all 0.2s ease;
}

.CollectionModal_metafieldRow__oh_Jn:hover {
  border-color: var(--color-primary-light);
  background: var(--color-primary-light);
}

.CollectionModal_metafieldInputs___GTXa {
  display: grid;
  grid-template-columns: 1fr 1fr 1.5fr 120px;
  gap: 12px;
  flex: 1;
}

.CollectionModal_metafieldInput__1oOaM,
.CollectionModal_metafieldSelect__Ykckg {
  padding: 12px 14px;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
  font-family: inherit;
}

.CollectionModal_metafieldInput__1oOaM:focus,
.CollectionModal_metafieldSelect__Ykckg:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
  transform: translateY(-1px);
}

.CollectionModal_removeMetafieldButton__3i1B7 {
  background: var(--color-error);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.CollectionModal_removeMetafieldButton__3i1B7:hover {
  background: var(--color-error-dark);
  transform: scale(1.05);
}

.CollectionModal_addMetafieldButton__dUxYK {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.CollectionModal_addMetafieldButton__dUxYK:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.CollectionModal_noMetafields__4_fAQ {
  color: var(--color-gray-600);
  font-style: italic;
  margin: 20px 0;
  text-align: center;
  padding: 32px;
  background: var(--color-gray-50);
  border-radius: 12px;
  border: 2px dashed var(--color-border);
  font-size: 15px;
}

.CollectionModal_metafieldsHelp__0McmH {
  color: var(--color-gray-600);
  font-size: 14px;
  line-height: 1.5;
  margin-top: 12px;
  padding: 16px;
  background: var(--color-blue-light);
  border-radius: 8px;
  border-left: 4px solid var(--color-primary);
}

/* Product Selection Styles */
.CollectionModal_productSelection__T4dG4 {
  border: 2px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.CollectionModal_productSelectionHeader__RhqTY {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--color-gray-50);
  border-bottom: 1px solid var(--color-border);
}

.CollectionModal_selectedCount__kCsYs {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 4px 12px;
  border-radius: 16px;
}

.CollectionModal_productList__X_KxM {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.CollectionModal_productItem__0gHK4 {
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.CollectionModal_productItem__0gHK4:hover {
  background: var(--color-gray-50);
  border-color: var(--color-border);
}

.CollectionModal_productCheckbox__Md8vu {
  display: flex !important;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  width: 100%;
}

.CollectionModal_productCheckbox__Md8vu input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.CollectionModal_productInfo__3S949 {
  flex: 1;
}

.CollectionModal_productInfo__3S949 h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 4px 0;
}

.CollectionModal_productInfo__3S949 p {
  font-size: 12px;
  color: var(--color-gray-500);
  margin: 0 0 4px 0;
  font-family: monospace;
}

.CollectionModal_productPrice__zoClP {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-primary);
}

.CollectionModal_noProducts__VZi9T {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-gray-600);
}

.CollectionModal_noProducts__VZi9T p {
  margin: 0;
  font-style: italic;
}

/* Children Selection Styles */
.CollectionModal_childrenSelection__39Dnl {
  border: 2px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.CollectionModal_childrenList__U4WvU {
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.CollectionModal_childCheckbox__HobOn {
  display: flex !important;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.CollectionModal_childCheckbox__HobOn:hover {
  background: var(--color-gray-50);
}

.CollectionModal_childCheckbox__HobOn input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.CollectionModal_childCheckbox__HobOn span {
  font-size: 14px;
  font-weight: 500;
  color: #2d2d2d;
}

/* Submit Error Styles */
.CollectionModal_submitError__Gco32 {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--color-error-light);
  color: var(--color-error);
  border-radius: 8px;
  margin: 16px 32px;
  font-size: 14px;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .CollectionModal_modalOverlay___vawn {
    padding: 10px;
  }
  
  .CollectionModal_modalContainer__4GZZw {
    max-height: 95vh;
  }
  
  .CollectionModal_modalHeader__24erq {
    padding: 16px;
  }
  
  .CollectionModal_modalForm__1KZOQ {
    padding: 16px;
  }
  
  .CollectionModal_formGrid__3mmYQ {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .CollectionModal_modalActions__qPhNr {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .CollectionModal_cancelButton__GjBkU,
  .CollectionModal_saveButton__4Tx_q {
    width: 100%;
    padding: 12px;
  }

  .CollectionModal_metafieldInputs___GTXa {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .CollectionModal_metafieldRow__oh_Jn {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .CollectionModal_removeMetafieldButton__3i1B7 {
    align-self: flex-end;
    width: -moz-fit-content;
    width: fit-content;
  }
}

/*!*************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[5].use[3]!./src/app/admin/collections/page.module.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************/
.page_collectionsPage__G6y4A {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page_header__2FUEd {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}

.page_title__DrTlr {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.page_subtitle__x0rin {
  font-size: 16px;
  color: #4a4a4a;
  margin: 0;
  font-weight: 500;
}

.page_headerActions__cszDS {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page_viewToggle__NJlHx {
  display: flex;
  background: var(--color-gray-100);
  border-radius: 8px;
  padding: 4px;
}

.page_toggleButton__bfFSD {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: transparent;
  color: #2d2d2d;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.page_toggleButton__bfFSD.page_active__1SicP {
  background: white;
  color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-weight: 700;
}

.page_toggleButton__bfFSD:hover:not(.page_active__1SicP) {
  color: #1a1a1a;
  background: rgba(255, 255, 255, 0.5);
}

.page_primaryButton__etF9g {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page_primaryButton__etF9g:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.page_secondaryButton__ZQjEx {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 18px;
  background: white;
  color: #2d2d2d;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.page_secondaryButton__ZQjEx:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #1a1a1a;
}

/* Toolbar Styles */
.page_toolbar__2ocpx {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  padding: 20px;
  margin-bottom: 24px;
}

.page_searchSection__GqroS {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.page_searchBox__g6Fs2 {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.page_searchBox__g6Fs2 svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
}

.page_searchInput__jsdiT {
  width: 100%;
  padding: 14px 14px 14px 44px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 15px;
  background: white;
  transition: all 0.2s;
  color: #1a1a1a;
  font-weight: 500;
}

.page_searchInput__jsdiT:focus {
  outline: none;
  border-color: var(--color-primary);
  background: white;
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.page_searchInput__jsdiT::-moz-placeholder {
  color: #6b7280;
  font-weight: 400;
}

.page_searchInput__jsdiT::placeholder {
  color: #6b7280;
  font-weight: 400;
}

.page_filterButton__1GOvt {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 18px;
  background: white;
  color: #2d2d2d;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.page_filterButton__1GOvt:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #1a1a1a;
}

.page_filterButton__1GOvt.page_active__1SicP {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page_filterButton__1GOvt svg.page_rotated__9tSXj {
  transform: rotate(180deg);
}

.page_filtersPanel__nEqfg {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 0 0 0;
  border-top: 1px solid var(--color-border);
  flex-wrap: wrap;
}

.page_filterGroup__ARJSU {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page_filterGroup__ARJSU label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
  white-space: nowrap;
}

.page_filterSelect__VoEIB {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: var(--color-gray-700);
  cursor: pointer;
}

.page_filterSelect__VoEIB:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.page_filterActions__2GLsf {
  margin-left: auto;
}

.page_clearFiltersButton__BVj22 {
  padding: 8px 16px;
  background: transparent;
  color: var(--color-gray-600);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.page_clearFiltersButton__BVj22:hover {
  background: var(--color-gray-50);
  color: var(--color-gray-800);
}

.page_content__aFiR9 {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 32px;
  height: calc(100vh - 200px);
}

.page_sidebar__Ub1d6 {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.page_mainContent__serbp {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.page_collectionDetails__3IXqJ {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page_detailsHeader__kqpXG {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-gray-50);
}

.page_collectionInfo__DiRoi h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.page_collectionMeta__tGpKZ {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.page_metaItem__2fgdT {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--color-gray-600);
}

.page_statusBadge__OpOQo {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_statusBadge__OpOQo.page_published__0ZkVw {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.page_statusBadge__OpOQo.page_draft__PFOvN {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.page_detailsActions__6RkGe {
  display: flex;
  gap: 12px;
}

.page_detailsContent__xI67v {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.page_descriptionSection__uocng,
.page_statsSection__9KrIn,
.page_childrenSection__nhamY {
  margin-bottom: 32px;
}

.page_descriptionSection__uocng h3,
.page_statsSection__9KrIn h3,
.page_childrenSection__nhamY h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 16px 0;
}

.page_descriptionSection__uocng p {
  font-size: 14px;
  color: var(--color-gray-700);
  line-height: 1.6;
  margin: 0;
}

.page_statsGrid__XjzQI {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.page_statCard__ouQOq {
  padding: 20px;
  background: var(--color-gray-50);
  border-radius: 8px;
  text-align: center;
}

.page_statValue__w3FaM {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.page_statLabel__l4UhV {
  font-size: 12px;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_childrenGrid__wDS9i {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.page_childCard__w_aKx {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-gray-50);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.page_childCard__w_aKx:hover {
  background: var(--color-gray-100);
  border-color: var(--color-primary);
}

.page_childIcon___PNaD {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.page_childInfo__WCctG {
  flex: 1;
}

.page_childInfo__WCctG h4 {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-900);
  margin: 0 0 4px 0;
}

.page_childInfo__WCctG p {
  font-size: 12px;
  color: var(--color-gray-600);
  margin: 0;
}

.page_childStatus__5h3mu {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_emptyState__V0Tkm {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-gray-600);
  text-align: center;
  padding: 40px;
}

.page_emptyState__V0Tkm svg {
  color: var(--color-gray-400);
  margin-bottom: 16px;
}

.page_emptyState__V0Tkm h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-700);
  margin: 0 0 8px 0;
}

.page_emptyState__V0Tkm p {
  font-size: 14px;
  color: var(--color-gray-600);
  margin: 0;
  max-width: 400px;
}

/* List View Styles */
.page_listView__3YBLw {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.page_loadingState__dhmkA {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--color-gray-600);
}

.page_spinner__gLYDy {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: page_spin__2O1ol 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes page_spin__2O1ol {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.page_collectionsTable__b_un7 {
  width: 100%;
}

.page_tableHeader__FBGml {
  background: var(--color-gray-50);
  border-bottom: 1px solid var(--color-border);
}

.page_tableBody__e3U8J {
  background: white;
}

.page_tableRow__MPVJV {
  display: grid;
  grid-template-columns: 2fr 100px 150px 120px 100px 120px 140px;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.2s;
  gap: 12px;
}

.page_tableRow__MPVJV:last-child {
  border-bottom: none;
}

.page_tableBody__e3U8J .page_tableRow__MPVJV:hover {
  background: var(--color-gray-50);
}

.page_tableHeader__FBGml .page_tableRow__MPVJV {
  font-weight: 700;
  color: #1a1a1a;
  font-size: 14px;
}

.page_tableCell__NrH3v {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #2d2d2d;
  font-weight: 500;
}

.page_collectionCell__GM7j6 {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page_collectionIcon__RThdB {
  color: var(--color-gray-500);
  display: flex;
  align-items: center;
}

.page_collectionInfo__DiRoi h4 {
  font-size: 15px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 2px 0;
}

.page_collectionInfo__DiRoi p {
  font-size: 13px;
  color: #4a4a4a;
  margin: 0 0 4px 0;
  font-family: monospace;
  font-weight: 500;
}

.page_collectionInfo__DiRoi .page_description___o_q2 {
  font-size: 12px;
  color: var(--color-gray-600);
  font-style: italic;
  display: block;
  margin-top: 4px;
}

.page_typeBadge__YKeet {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.page_typeBadge__YKeet.page_manual__E1VGq {
  background: var(--color-blue-light);
  color: var(--color-blue-dark);
}

.page_typeBadge__YKeet.page_smart__T1pWL {
  background: var(--color-purple-light);
  color: var(--color-purple-dark);
}

.page_levelBadge__jJKPE {
  padding: 4px 8px;
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.page_productCount__lRgZl {
  font-weight: 600;
  color: var(--color-gray-900);
}

.page_parentInfo__bCLnS {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #d1d5db;
}

.page_noParent__F9P1A {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.page_childrenCount__iGLwt {
  font-size: 13px;
  color: #059669;
  font-weight: 500;
  background: #ecfdf5;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #a7f3d0;
}

.page_noChildren__FGw1V {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.page_actionButtons__aHLxZ {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page_actionButton__zmzZy {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  color: var(--color-gray-600);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.page_actionButton__zmzZy:hover {
  background: var(--color-gray-50);
  color: var(--color-gray-800);
  border-color: var(--color-gray-300);
}

.page_actionButton__zmzZy.page_deleteButton__jFxJ2:hover {
  background: var(--color-error-light);
  color: var(--color-error);
  border-color: var(--color-error);
}

/* Responsive design */
@media (max-width: 1024px) {
  .page_content__aFiR9 {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .page_sidebar__Ub1d6 {
    order: 2;
  }

  .page_mainContent__serbp {
    order: 1;
  }

  .page_tableRow__MPVJV {
    grid-template-columns: 2fr 80px 100px 80px 100px;
    gap: 8px;
  }

  .page_tableCell__NrH3v:nth-child(3),
  .page_tableCell__NrH3v:nth-child(4) {
    display: none; /* Hide parent and children on medium screens */
  }
}

@media (max-width: 768px) {
  .page_collectionsPage__G6y4A {
    padding: 16px;
  }

  .page_header__2FUEd {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .page_headerActions__cszDS {
    flex-direction: column;
    gap: 12px;
  }

  .page_viewToggle__NJlHx {
    align-self: stretch;
  }

  .page_title__DrTlr {
    font-size: 24px;
  }

  .page_content__aFiR9 {
    height: auto;
    min-height: 600px;
  }

  .page_searchSection__GqroS {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .page_searchBox__g6Fs2 {
    max-width: none;
  }

  .page_filtersPanel__nEqfg {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .page_filterActions__2GLsf {
    margin-left: 0;
  }

  .page_tableRow__MPVJV {
    grid-template-columns: 1fr 60px;
    gap: 8px;
    padding: 12px 16px;
  }

  .page_tableCell__NrH3v:nth-child(2),
  .page_tableCell__NrH3v:nth-child(3),
  .page_tableCell__NrH3v:nth-child(4),
  .page_tableCell__NrH3v:nth-child(5),
  .page_tableCell__NrH3v:nth-child(6) {
    display: none; /* Hide level, parent, children, products, status on mobile */
  }

  .page_collectionInfo__DiRoi .page_description___o_q2 {
    display: none; /* Hide description on mobile */
  }

  .page_actionButtons__aHLxZ {
    flex-direction: column;
    gap: 4px;
  }

  .page_actionButton__zmzZy {
    width: 28px;
    height: 28px;
  }

  .page_detailsHeader__kqpXG {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .page_detailsActions__6RkGe {
    justify-content: stretch;
  }

  .page_detailsActions__6RkGe button {
    flex: 1;
  }

  .page_statsGrid__XjzQI {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .page_childrenGrid__wDS9i {
    grid-template-columns: 1fr;
  }
}

