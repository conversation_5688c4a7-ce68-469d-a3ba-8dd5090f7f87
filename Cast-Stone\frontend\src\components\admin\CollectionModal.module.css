.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 16px;
  backdrop-filter: blur(6px);
}

.modalContainer {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 1000px;
  height: 90vh;
  max-height: 800px;
  overflow: hidden;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid var(--color-border);
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
}

.modalHeader h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  color: black;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.closeButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.2);
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  color: black;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.closeButton:hover {
  background: rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.modalForm {
  padding: 40px;
  flex: 1;
  overflow-y: auto;
  background: #f8fafc;
}

.formGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.formSection {
  display: flex;
  flex-direction: column;
  gap: 28px;
  background: white;
  padding: 40px;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.formSection h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  /* font-weight: 700; */
  /* color: var(--color-gray-900); */
  font-color: black;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--color-primary);
  position: relative;
}

.formSection h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--color-primary);
  border-radius: 1px;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.formGroup label {
  font-size: 15px;
  font-weight: 2000;
  color: var(--color-gray-800);
  margin-bottom: 4px;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  padding: 14px 16px;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  font-size: 15px;
  background: white;
  transition: all 0.2s ease;
  font-family: inherit;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 4px var(--color-primary-light);
  transform: translateY(-1px);
}

.formGroup textarea {
  resize: vertical;
  min-height: 80px;
}

.formGroup small {
  font-size: 13px;
  color: var(--color-gray-600);
  line-height: 1.4;
  margin-top: 2px;
}

.inputError {
  border-color: var(--color-error) !important;
  box-shadow: 0 0 0 4px var(--color-error-light) !important;
  background: var(--color-error-light) !important;
}

.errorText {
  font-size: 13px;
  color: var(--color-error);
  margin-top: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.checkboxLabel {
  display: flex !important;
  flex-direction: row !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkboxLabel input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.checkboxLabel span {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
}

.uploadArea {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  border: 2px dashed var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  color: var(--color-gray-600);
}

.uploadArea:hover {
  border-color: var(--color-primary);
  background: var(--color-primary-light);
}

.uploadArea p {
  margin: 8px 0 4px 0;
  font-weight: 500;
}

.uploadArea small {
  color: var(--color-gray-500);
}

.submitError {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--color-error-light);
  color: var(--color-error-dark);
  border-radius: 6px;
  font-size: 14px;
  margin-top: 20px;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 20px;
  margin-top: 40px;
  padding: 24px 32px;
  border-top: 2px solid black;
  background: white;
  border-top: 1px solid var(--color-border);
  background: white;
}

.cancelButton {
  padding: 16px 28px;
  background: white;
  color: black;
  border: 2px solid black;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.cancelButton:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
}

.saveButton {
  padding: 16px 28px;
  background: var(--color-primary);
  color: white;
  border: 2px solid black;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 160px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-left: 16px;
}

.saveButton:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Metafields Styling */
.metafieldsContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
}

.metafieldRow {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: var(--color-gray-50);
  border-radius: 12px;
  border: 2px solid var(--color-border);
  transition: all 0.2s ease;
}

.metafieldRow:hover {
  border-color: var(--color-primary-light);
  background: var(--color-primary-light);
}

.metafieldInputs {
  display: grid;
  grid-template-columns: 1fr 1fr 1.5fr 120px;
  gap: 12px;
  flex: 1;
}

.metafieldInput,
.metafieldSelect {
  padding: 12px 14px;
  border: 2px solid var(--color-border);
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
  font-family: inherit;
}

.metafieldInput:focus,
.metafieldSelect:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px var(--color-primary-light);
  transform: translateY(-1px);
}

.removeMetafieldButton {
  background: var(--color-error);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
}

.removeMetafieldButton:hover {
  background: var(--color-error-dark);
  transform: scale(1.05);
}

.addMetafieldButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.addMetafieldButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.noMetafields {
  color: var(--color-gray-600);
  font-style: italic;
  margin: 20px 0;
  text-align: center;
  padding: 32px;
  background: var(--color-gray-50);
  border-radius: 12px;
  border: 2px dashed var(--color-border);
  font-size: 15px;
}

.metafieldsHelp {
  color: var(--color-gray-600);
  font-size: 14px;
  line-height: 1.5;
  margin-top: 12px;
  padding: 16px;
  background: var(--color-blue-light);
  border-radius: 8px;
  border-left: 4px solid var(--color-primary);
}

/* Product Selection Styles */
.productSelection {
  border: 2px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.productSelectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--color-gray-50);
  border-bottom: 1px solid var(--color-border);
}

.selectedCount {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-primary);
  background: var(--color-primary-light);
  padding: 4px 12px;
  border-radius: 16px;
}

.productList {
  max-height: 300px;
  overflow-y: auto;
  padding: 8px;
}

.productItem {
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
}

.productItem:hover {
  background: var(--color-gray-50);
  border-color: var(--color-border);
}

.productCheckbox {
  display: flex !important;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  width: 100%;
}

.productCheckbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.productInfo {
  flex: 1;
}

.productInfo h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 4px 0;
}

.productInfo p {
  font-size: 12px;
  color: var(--color-gray-500);
  margin: 0 0 4px 0;
  font-family: monospace;
}

.productPrice {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-primary);
}

.noProducts {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-gray-600);
}

.noProducts p {
  margin: 0;
  font-style: italic;
}

/* Children Selection Styles */
.childrenSelection {
  border: 2px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
  background: white;
}

.childrenList {
  padding: 16px;
  max-height: 200px;
  overflow-y: auto;
}

.childCheckbox {
  display: flex !important;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  margin-bottom: 8px;
}

.childCheckbox:hover {
  background: var(--color-gray-50);
}

.childCheckbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.childCheckbox span {
  font-size: 14px;
  font-weight: 500;
  color: #2d2d2d;
}

/* Submit Error Styles */
.submitError {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--color-error-light);
  color: var(--color-error);
  border-radius: 8px;
  margin: 16px 32px;
  font-size: 14px;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .modalOverlay {
    padding: 10px;
  }
  
  .modalContainer {
    max-height: 95vh;
  }
  
  .modalHeader {
    padding: 16px;
  }
  
  .modalForm {
    padding: 16px;
  }
  
  .formGrid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .modalActions {
    flex-direction: column-reverse;
    gap: 8px;
  }
  
  .cancelButton,
  .saveButton {
    width: 100%;
    padding: 12px;
  }

  .metafieldInputs {
    grid-template-columns: 1fr;
    gap: 6px;
  }

  .metafieldRow {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .removeMetafieldButton {
    align-self: flex-end;
    width: fit-content;
  }
}
