/**
 * Debug Login Flow
 * Step-by-step debugging of the login process
 */

const fetch = require('node-fetch');

async function debugLoginFlow() {
  console.log('🔍 Debug Login Flow');
  console.log('===================\n');
  
  try {
    // Step 1: Test backend login
    console.log('📍 Step 1: Test backend login API');
    const loginResponse = await fetch('http://localhost:5000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '132Trent@!'
      })
    });
    
    const loginResult = await loginResponse.json();
    console.log('Login response status:', loginResponse.status);
    console.log('Login response:', JSON.stringify(loginResult, null, 2));
    
    if (!loginResult.success) {
      console.log('❌ Backend login failed');
      return false;
    }
    
    const token = loginResult.token;
    console.log('✅ Backend login successful');
    console.log('Token:', token.substring(0, 30) + '...');
    
    // Step 2: Test token verification
    console.log('\n📍 Step 2: Test token verification');
    const verifyResponse = await fetch('http://localhost:5000/api/admin/verify-token', {
      headers: { 
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const verifyResult = await verifyResponse.json();
    console.log('Verify response status:', verifyResponse.status);
    console.log('Verify response:', JSON.stringify(verifyResult, null, 2));
    
    if (!verifyResult.success) {
      console.log('❌ Token verification failed');
      return false;
    }
    
    console.log('✅ Token verification successful');
    
    // Step 3: Test frontend login page
    console.log('\n📍 Step 3: Test frontend login page');
    const frontendResponse = await fetch('http://localhost:3000/admin/login');
    console.log('Frontend login page status:', frontendResponse.status);
    
    if (frontendResponse.status !== 200) {
      console.log('❌ Frontend login page not accessible');
      return false;
    }
    
    console.log('✅ Frontend login page accessible');
    
    // Step 4: Test dashboard page (should redirect to login)
    console.log('\n📍 Step 4: Test dashboard page (without auth)');
    const dashboardResponse = await fetch('http://localhost:3000/admin/dashboard', {
      redirect: 'manual' // Don't follow redirects
    });
    
    console.log('Dashboard response status:', dashboardResponse.status);
    console.log('Dashboard response headers:', Object.fromEntries(dashboardResponse.headers.entries()));
    
    if (dashboardResponse.status === 307 || dashboardResponse.status === 302) {
      const location = dashboardResponse.headers.get('location');
      console.log('✅ Dashboard correctly redirects to:', location);
    } else {
      console.log('⚠️  Dashboard response unexpected:', dashboardResponse.status);
    }
    
    // Step 5: Simulate frontend auth flow
    console.log('\n📍 Step 5: Simulate frontend auth state');
    
    // This simulates what the frontend auth service should do
    const authState = {
      isAuthenticated: true,
      admin: loginResult.admin,
      token: token,
      isLoading: false,
      error: null
    };
    
    console.log('Simulated auth state:', JSON.stringify(authState, null, 2));
    
    // Check if admin data is complete
    const requiredFields = ['id', 'email', 'name', 'role'];
    const missingFields = requiredFields.filter(field => !authState.admin[field]);
    
    if (missingFields.length > 0) {
      console.log('❌ Missing admin fields:', missingFields);
      return false;
    }
    
    console.log('✅ Admin data complete');
    
    console.log('\n🎯 Summary:');
    console.log('- Backend login: ✅ Working');
    console.log('- Token verification: ✅ Working');
    console.log('- Frontend login page: ✅ Accessible');
    console.log('- Dashboard redirect: ✅ Working');
    console.log('- Admin data: ✅ Complete');
    
    console.log('\n💡 The backend is working correctly.');
    console.log('💡 The issue is likely in the frontend authentication state management.');
    console.log('💡 Check browser console for frontend errors.');
    
    return true;
    
  } catch (error) {
    console.log('❌ Debug failed:', error.message);
    console.log('Stack:', error.stack);
    return false;
  }
}

// Run debug
debugLoginFlow().then(success => {
  console.log('\n' + '='.repeat(50));
  console.log(success ? '✅ DEBUG COMPLETE' : '❌ DEBUG FAILED');
  console.log('='.repeat(50));
}).catch(error => {
  console.error('Debug error:', error);
});
