/**
 * Test Cookie Fix
 * Tests that the authentication token is properly stored in cookies
 */

const fetch = require('node-fetch');

async function testCookieFix() {
  console.log('🍪 Testing Cookie Fix for Admin Authentication');
  console.log('==============================================\n');
  
  try {
    // Step 1: Test backend login to get token
    console.log('📍 Step 1: Get authentication token from backend');
    const loginResponse = await fetch('http://localhost:5000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '132Trent@!'
      })
    });
    
    const loginResult = await loginResponse.json();
    
    if (!loginResult.success) {
      throw new Error('Backend login failed: ' + loginResult.message);
    }
    
    const token = loginResult.token;
    console.log('✅ Token received:', token.substring(0, 30) + '...');
    
    // Step 2: Test middleware with token in cookie
    console.log('\n📍 Step 2: Test middleware with token in cookie');
    
    // Simulate cookie header
    const cookieHeader = `adminToken=${token}`;
    
    const dashboardResponse = await fetch('http://localhost:3000/admin/dashboard', {
      headers: {
        'Cookie': cookieHeader
      },
      redirect: 'manual'
    });
    
    console.log('Dashboard response status:', dashboardResponse.status);
    const location = dashboardResponse.headers.get('location');
    
    if (dashboardResponse.status === 200) {
      console.log('✅ Dashboard accessible with cookie token!');
      console.log('✅ Middleware correctly recognizes authentication');
    } else if (dashboardResponse.status === 307 && location) {
      console.log('❌ Still redirecting to:', location);
      console.log('❌ Middleware not recognizing cookie token');
    } else {
      console.log('⚠️  Unexpected response:', dashboardResponse.status);
    }
    
    // Step 3: Test middleware without token
    console.log('\n📍 Step 3: Test middleware without token (should redirect)');
    
    const noAuthResponse = await fetch('http://localhost:3000/admin/dashboard', {
      redirect: 'manual'
    });
    
    console.log('No auth response status:', noAuthResponse.status);
    const noAuthLocation = noAuthResponse.headers.get('location');
    
    if (noAuthResponse.status === 307 && noAuthLocation && noAuthLocation.includes('/admin/login')) {
      console.log('✅ Correctly redirects to login without token');
    } else {
      console.log('⚠️  Unexpected behavior without token');
    }
    
    // Step 4: Test token verification with cookie
    console.log('\n📍 Step 4: Test backend token verification');
    
    const verifyResponse = await fetch('http://localhost:5000/api/admin/verify-token', {
      headers: {
        'Cookie': cookieHeader
      }
    });
    
    const verifyResult = await verifyResponse.json();
    console.log('Verify response status:', verifyResponse.status);
    console.log('Verify success:', verifyResult.success);
    
    if (verifyResult.success) {
      console.log('✅ Backend accepts cookie token for verification');
    } else {
      console.log('❌ Backend rejects cookie token');
    }
    
    console.log('\n🎯 Summary:');
    console.log('- Backend login: ✅ Working');
    console.log('- Token generation: ✅ Working');
    console.log('- Middleware with cookie:', dashboardResponse.status === 200 ? '✅ Working' : '❌ Not working');
    console.log('- Middleware without cookie:', noAuthResponse.status === 307 ? '✅ Working' : '❌ Not working');
    console.log('- Backend cookie verification:', verifyResult.success ? '✅ Working' : '❌ Not working');
    
    if (dashboardResponse.status === 200) {
      console.log('\n🎉 Cookie fix is working! The middleware should now properly recognize authentication.');
      console.log('💡 Try logging in again - it should redirect to dashboard successfully.');
    } else {
      console.log('\n⚠️  Cookie fix needs more work. The middleware is still not recognizing the token.');
    }
    
    return dashboardResponse.status === 200;
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  }
}

// Run test
testCookieFix().then(success => {
  console.log('\n' + '='.repeat(50));
  console.log(success ? '✅ COOKIE FIX WORKING' : '❌ COOKIE FIX NEEDS MORE WORK');
  console.log('='.repeat(50));
}).catch(error => {
  console.error('Test error:', error);
});
