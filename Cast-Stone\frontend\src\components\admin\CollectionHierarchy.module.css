.hierarchyContainer {
  background: white;
  border-radius: 8px;
  border: 1px solid var(--cart-border);
  overflow: hidden;
}

.hierarchyHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--cart-border);
  background: var(--background-light);
}

.hierarchyHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
}

.addRootButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addRootButton:hover {
  background: var(--cart-primary-hover);
}

.hierarchyTree {
  max-height: 600px;
  overflow-y: auto;
}

.collectionNode {
  border-bottom: 1px solid var(--cart-border);
}

.collectionNode:last-child {
  border-bottom: none;
}

.collectionItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  cursor: pointer;
  transition: background-color 0.2s;
  min-height: 60px;
}

.collectionItem:hover {
  background: var(--background-light);
}

.collectionItem.selected {
  background: var(--secondary-color);
  border-left: 3px solid var(--primary-color);
}

.collectionContent {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 8px;
}

.expandButton {
  width: 20px;
  display: flex;
  justify-content: center;
}

.expandToggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.expandToggle:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.expandSpacer {
  width: 20px;
}

.collectionIcon {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.collectionInfo {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.collectionTitle {
  font-weight: 500;
  color: var(--color-gray-900);
  font-size: 14px;
}

.collectionMeta {
  font-size: 12px;
  color: var(--color-gray-600);
}

.collectionStatus {
  margin-right: 12px;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusBadge.published {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.statusBadge.draft {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.collectionActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
  margin-right: 12px;
}

.collectionItem:hover .collectionActions {
  opacity: 1;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: var(--color-gray-600);
  transition: all 0.2s;
}

.actionButton:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-800);
}

.childrenContainer {
  border-left: 1px solid var(--color-border-light);
  margin-left: 20px;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-border);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.errorContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  gap: 16px;
}

.errorMessage {
  color: var(--color-error);
  text-align: center;
  margin: 0;
}

.retryButton {
  padding: 8px 16px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.retryButton:hover {
  background: var(--color-primary-dark);
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  gap: 16px;
  color: var(--color-gray-600);
}

.emptyState svg {
  color: var(--color-gray-400);
}

.emptyState p {
  margin: 0;
  font-size: 16px;
  color: var(--color-gray-600);
}

.addFirstButton {
  padding: 12px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.addFirstButton:hover {
  background: var(--color-primary-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .hierarchyHeader {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .collectionItem {
    padding: 8px 0;
    min-height: 50px;
  }
  
  .collectionActions {
    opacity: 1;
  }
  
  .actionButton {
    width: 32px;
    height: 32px;
  }
}
