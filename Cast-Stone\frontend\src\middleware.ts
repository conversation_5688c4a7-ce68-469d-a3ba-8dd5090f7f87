/**
 * Next.js Middleware
 * Handles authentication and route protection
 */

import { NextRequest, NextResponse } from 'next/server';
import { adminAuthMiddleware } from './middleware/adminAuth';

export function middleware(request: NextRequest) {
  // Handle admin authentication
  if (request.nextUrl.pathname.startsWith('/admin')) {
    return adminAuthMiddleware(request);
  }
  
  // Allow all other routes
  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
