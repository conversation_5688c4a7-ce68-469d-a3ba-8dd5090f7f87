'use client';

import { useState, useEffect } from 'react';
import { X, AlertCircle } from 'lucide-react';
import { collectionsApi } from '../../services/api';
import styles from './CollectionModal.module.css';

interface Collection {
  _id?: string;
  title: string;
  handle?: string;
  description: string;
  collectionType?: 'manual' | 'smart';
  parent?: string;
  children?: string[];
  published: boolean;
  level?: number;
  path?: string;
}

interface CollectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (collection: Collection) => void;
  collection?: Collection | null;
  parentCollection?: Collection | null;
  mode: 'create' | 'edit';
}

export default function CollectionModal({
  isOpen,
  onClose,
  onSave,
  collection,
  parentCollection,
  mode
}: CollectionModalProps) {
  const [formData, setFormData] = useState<Collection>({
    title: '',
    description: '',
    collectionType: 'manual',
    published: false,
    children: []
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoading, setIsLoading] = useState(false);
  const [availableParents, setAvailableParents] = useState<Collection[]>([]);
  const [availableChildren, setAvailableChildren] = useState<Collection[]>([]);

  useEffect(() => {
    if (isOpen) {
      if (mode === 'edit' && collection) {
        setFormData({
          ...collection,
          children: collection.children || []
        });
      } else if (mode === 'create') {
        setFormData({
          title: '',
          description: '',
          collectionType: 'manual',
          published: false,
          parent: parentCollection?._id || '',
          children: []
        });
      }
      fetchAvailableParents();
      fetchAvailableChildren();
    }
  }, [isOpen, mode, collection, parentCollection]);

  const fetchAvailableParents = async () => {
    try {
      const response = await collectionsApi.getCollections({
        limit: 100
      });

      const validParents = response.collections?.filter((c: Collection) =>
        c.level !== undefined && c.level < 2 && c._id !== collection?._id
      ) || [];

      setAvailableParents(validParents);
    } catch (error) {
      console.error('Failed to fetch available parents:', error);
    }
  };

  const fetchAvailableChildren = async () => {
    try {
      const response = await collectionsApi.getCollections({
        limit: 100
      });

      const validChildren = response.collections?.filter((c: Collection) =>
        c.level !== undefined && c.level < 3 && c._id !== collection?._id && c._id !== formData.parent
      ) || [];

      setAvailableChildren(validChildren);
    } catch (error) {
      console.error('Failed to fetch available children:', error);
    }
  };

  const generateHandle = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };

      if (field === 'title') {
        const handle = generateHandle(value);
        updated.handle = handle;
      }

      return updated;
    });

    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleChildSelection = (childId: string, selected: boolean) => {
    setFormData(prev => {
      const children = prev.children || [];
      if (selected) {
        return { ...prev, children: [...children, childId] };
      } else {
        return { ...prev, children: children.filter(id => id !== childId) };
      }
    });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Collection name is required';
    }

    if (!formData.collectionType) {
      newErrors.collectionType = 'Collection type is required';
    }

    if (formData.parent) {
      const parent = availableParents.find(p => p._id === formData.parent);
      if (parent && parent.level !== undefined && parent.level >= 2) {
        newErrors.parent = 'Cannot add sub-collection to this level';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    try {
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('Failed to save collection:', error);
      setErrors(prev => ({
        ...prev,
        submit: 'Failed to save collection. Please try again.'
      }));
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  const modalTitle = mode === 'create'
    ? (parentCollection ? `Add Sub-Collection to "${parentCollection.title}"` : 'Create New Collection')
    : `Edit "${collection?.title}"`;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContainer}>
        <div className={styles.modalHeader}>
          <h2>{modalTitle}</h2>
          <button onClick={onClose} className={styles.closeButton}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className={styles.modalForm}>
          <div className={styles.formGrid}>
            <div className={styles.formSection}>
              <h3>Collection Information</h3>

              <div className={styles.formGroup}>
                <label htmlFor="title">Collection Name *</label>
                <input
                  id="title"
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  className={errors.title ? styles.inputError : ''}
                  placeholder="Enter collection name"
                />
                {errors.title && <span className={styles.errorText}>{errors.title}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="description">Description</label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  className={errors.description ? styles.inputError : ''}
                  placeholder="Enter collection description"
                  rows={4}
                />
                {errors.description && <span className={styles.errorText}>{errors.description}</span>}
              </div>

              <div className={styles.formGroup}>
                <label htmlFor="collectionType">Collection Type *</label>
                <select
                  id="collectionType"
                  value={formData.collectionType || 'manual'}
                  onChange={(e) => handleInputChange('collectionType', e.target.value)}
                  className={errors.collectionType ? styles.inputError : ''}
                >
                  <option value="manual">Manual</option>
                  <option value="smart">Smart (Automatic)</option>
                </select>
                {errors.collectionType && <span className={styles.errorText}>{errors.collectionType}</span>}
                <small>Manual collections require you to add products manually. Smart collections automatically include products based on rules.</small>
              </div>

              {mode === 'create' && (
                <div className={styles.formGroup}>
                  <label htmlFor="parent">Parent Collection</label>
                  <select
                    id="parent"
                    value={formData.parent || ''}
                    onChange={(e) => handleInputChange('parent', e.target.value)}
                    className={errors.parent ? styles.inputError : ''}
                  >
                    <option value="">None (Root Collection)</option>
                    {availableParents.map((parent) => (
                      <option key={parent._id} value={parent._id}>
                        {'  '.repeat(parent.level || 0)}{parent.title} (Level {parent.level})
                      </option>
                    ))}
                  </select>
                  {errors.parent && <span className={styles.errorText}>{errors.parent}</span>}
                  <small>Select a parent collection to create a sub-collection</small>
                </div>
              )}

              {availableChildren.length > 0 && (
                <div className={styles.formGroup}>
                  <label>Child Collections</label>
                  <div className={styles.childrenSelection}>
                    <small>Select collections to be children of this collection:</small>
                    <div className={styles.childrenList}>
                      {availableChildren.map((child) => (
                        <label key={child._id} className={styles.childCheckbox}>
                          <input
                            type="checkbox"
                            checked={formData.children?.includes(child._id) || false}
                            onChange={(e) => handleChildSelection(child._id, e.target.checked)}
                          />
                          <span>{child.title} (Level {child.level})</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className={styles.formGroup}>
                <label className={styles.checkboxLabel}>
                  <input
                    type="checkbox"
                    checked={formData.published}
                    onChange={(e) => handleInputChange('published', e.target.checked)}
                  />
                  <span>Published</span>
                </label>
                <small>Published collections are visible to customers</small>
              </div>
            </div>
          </div>

          {errors.submit && (
            <div className={styles.submitError}>
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}

          <div className={styles.modalActions}>
            <button type="button" onClick={onClose} className={styles.cancelButton}>
              Cancel
            </button>
            <button type="submit" disabled={isLoading} className={styles.saveButton}>
              {isLoading ? 'Saving...' : (mode === 'create' ? 'Create Collection' : 'Save Changes')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}