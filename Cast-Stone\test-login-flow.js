/**
 * Test Admin Login Flow
 * Tests the complete login and redirect flow
 */

const puppeteer = require('puppeteer');

async function testLoginFlow() {
  let browser;
  
  try {
    console.log('🚀 Testing Admin Login Flow');
    console.log('============================\n');
    
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Enable console logging
    page.on('console', msg => {
      console.log(`🖥️  Browser: ${msg.text()}`);
    });
    
    page.on('pageerror', error => {
      console.log(`❌ Page Error: ${error.message}`);
    });
    
    // Navigate to admin login
    console.log('📍 Step 1: Navigate to login page');
    await page.goto('http://localhost:3000/admin/login', { 
      waitUntil: 'networkidle0',
      timeout: 10000
    });
    
    console.log('✅ Login page loaded');
    
    // Check if login form is present
    console.log('📍 Step 2: Check login form');
    await page.waitForSelector('form', { timeout: 5000 });
    await page.waitForSelector('input[type="email"]', { timeout: 5000 });
    await page.waitForSelector('input[type="password"]', { timeout: 5000 });
    await page.waitForSelector('button[type="submit"]', { timeout: 5000 });
    
    console.log('✅ Login form elements found');
    
    // Fill in credentials
    console.log('📍 Step 3: Fill in credentials');
    await page.type('input[type="email"]', '<EMAIL>');
    await page.type('input[type="password"]', '132Trent@!');
    
    console.log('✅ Credentials entered');
    
    // Submit form and wait for navigation
    console.log('📍 Step 4: Submit form and wait for redirect');
    
    // Set up navigation promise before clicking
    const navigationPromise = page.waitForNavigation({ 
      waitUntil: 'networkidle0',
      timeout: 15000 
    });
    
    // Click submit button
    await page.click('button[type="submit"]');
    
    // Wait for navigation
    await navigationPromise;
    
    // Check final URL
    const finalUrl = page.url();
    console.log('📍 Final URL:', finalUrl);
    
    if (finalUrl.includes('/admin/dashboard')) {
      console.log('✅ Successfully redirected to dashboard');
      
      // Wait for dashboard content to load
      await page.waitForSelector('h1', { timeout: 5000 });
      const title = await page.$eval('h1', el => el.textContent);
      
      if (title && title.includes('Dashboard')) {
        console.log('✅ Dashboard content loaded');
        console.log('🎉 LOGIN FLOW TEST PASSED!');
        return true;
      } else {
        console.log('❌ Dashboard title not found:', title);
        return false;
      }
    } else if (finalUrl.includes('/admin/login')) {
      console.log('❌ Still on login page - redirect failed');
      
      // Check for error messages
      try {
        const errorElement = await page.$('.errorAlert, .error, [role="alert"]');
        if (errorElement) {
          const errorText = await page.evaluate(el => el.textContent, errorElement);
          console.log('❌ Error message:', errorText);
        }
      } catch (e) {
        console.log('❌ No specific error message found');
      }
      
      return false;
    } else {
      console.log('❌ Unexpected redirect to:', finalUrl);
      return false;
    }
    
  } catch (error) {
    console.log('❌ Test failed with error:', error.message);
    return false;
  } finally {
    if (browser) {
      // Keep browser open for 5 seconds to see the result
      console.log('⏳ Keeping browser open for 5 seconds...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      await browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  testLoginFlow().then(success => {
    console.log('\n' + '='.repeat(40));
    if (success) {
      console.log('🎉 TEST RESULT: PASSED');
    } else {
      console.log('❌ TEST RESULT: FAILED');
    }
    console.log('='.repeat(40));
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('❌ Test error:', error);
    process.exit(1);
  });
}

module.exports = testLoginFlow;
