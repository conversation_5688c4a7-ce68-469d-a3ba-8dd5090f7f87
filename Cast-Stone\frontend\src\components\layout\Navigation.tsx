'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import styles from './Navigation.module.css';
import { CartIcon, CartSidebar } from '../index';

interface NavigationProps {
  className?: string;
}

interface Collection {
  _id: string;
  title: string;
  handle: string;
  level: number;
  children: Collection[];
}

export default function Navigation({ className }: NavigationProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileDropdowns, setMobileDropdowns] = useState({
    company: false,
    products: false,
    discover: false
  });
  const [collections, setCollections] = useState<Collection[]>([]);
  const [showMegaMenu, setShowMegaMenu] = useState(false);

  // Fetch collections for mega menu
  useEffect(() => {
    const fetchCollections = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/collections?hierarchy=true');
        if (response.ok) {
          const data = await response.json();
          // Handle hierarchical response structure
          if (data.success && data.data) {
            if (data.data.hierarchy) {
              // Hierarchical structure: data.data.collections
              setCollections(data.data.collections || []);
            } else {
              // Regular structure: data.data.collections or data.data
              setCollections(data.data.collections || data.data || []);
            }
          }
        }
      } catch (error) {
        console.error('Failed to fetch collections:', error);
        setCollections([]); // Set empty array on error to prevent filter issues
      }
    };

    fetchCollections();
  }, []);

  return (
    <nav className={`${styles.navigation} ${className || ''}`}>
      <div className={styles.navContainer}>
        <div className={styles.logo}>
          <h1>Cast Stone</h1>
          <span>Interiors & Decorations</span>
        </div>

        {/* Desktop Menu */}
        <div className={styles.navMenuWrapper}>
          <ul className={styles.navMenu}>
            <li className={styles.dropdown}>
              <a href="#" className={styles.dropdownToggle}>Company</a>
              <ul className={styles.dropdownMenu}>
                <li><Link href="/contact">Contact Us</Link></li>
                <li><Link href="/about">Our Story</Link></li>
                <li><Link href="/retail-locator">Retail Locator</Link></li>
                <li><Link href="/wholesale-signup">Wholesale Sign-up</Link></li>
              </ul>
            </li>
            <li
              className={`${styles.dropdown} ${styles.megaDropdown}`}
              onMouseEnter={() => setShowMegaMenu(true)}
              onMouseLeave={() => setShowMegaMenu(false)}
            >
              <a href="#" className={styles.dropdownToggle}>Products</a>
              {showMegaMenu && (
                <div className={styles.megaMenu}>
                  <div className={styles.megaMenuContent}>
                    <div className={styles.megaMenuSection}>
                      <h3>Main Collections</h3>
                      <div className={styles.collectionsGrid}>
                        {collections && collections.length > 0 ? (
                          collections
                            .filter(c => c && c.level === 0)
                            .map((collection) => (
                              <div key={collection._id} className={styles.collectionGroup}>
                                <Link
                                  href={`/collections/${collection.handle}`}
                                  className={styles.collectionTitle}
                                >
                                  {collection.title}
                                </Link>
                                {collection.children && collection.children.length > 0 && (
                                  <ul className={styles.subCollectionsList}>
                                    {collection.children.map((subCollection) => (
                                      <li key={subCollection._id}>
                                        <Link href={`/collections/${subCollection.handle}`}>
                                          {subCollection.title}
                                        </Link>
                                        {subCollection.children && subCollection.children.length > 0 && (
                                          <ul className={styles.subSubCollectionsList}>
                                            {subCollection.children.map((subSubCollection) => (
                                              <li key={subSubCollection._id}>
                                                <Link href={`/collections/${subSubCollection.handle}`}>
                                                  {subSubCollection.title}
                                                </Link>
                                              </li>
                                            ))}
                                          </ul>
                                        )}
                                      </li>
                                    ))}
                                  </ul>
                                )}
                              </div>
                            ))
                        ) : (
                          <div className={styles.loadingMessage}>Loading collections...</div>
                        )}
                      </div>
                    </div>
                    <div className={styles.megaMenuSection}>
                      <h3>Quick Links</h3>
                      <ul className={styles.quickLinks}>
                        <li><Link href="/products">All Products</Link></li>
                        <li><Link href="/collections">Browse Collections</Link></li>
                        <li><Link href="/catalog">Product Catalog</Link></li>
                        <li><Link href="/finishes">Available Finishes</Link></li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </li>
            <li><Link href="/projects">Completed Projects</Link></li>
            <li className={styles.dropdown}>
              <a href="#" className={styles.dropdownToggle}>Discover</a>
              <ul className={styles.dropdownMenu}>
                <li><Link href="/catalog">Catalog</Link></li>
                <li><Link href="/finishes">Finishes</Link></li>
                <li><Link href="/videos">Videos</Link></li>
                <li><Link href="/technical">Technical Info</Link></li>
                <li><Link href="/faqs">FAQs</Link></li>
              </ul>
            </li>
          </ul>
        </div>

        {/* Cart Icon */}
        <div className={styles.cartWrapper}>
          <CartIcon />
        </div>

        {/* Mobile Menu Toggle */}
        <div
          className={`${styles.mobileMenuToggle} ${mobileMenuOpen ? styles.active : ''}`}
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          <div className={styles.hamburgerLine}></div>
          <div className={styles.hamburgerLine}></div>
          <div className={styles.hamburgerLine}></div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div className={`${styles.mobileMenu} ${mobileMenuOpen ? styles.active : ''}`}>
        <div className={styles.mobileMenuLogo}>
          <h1>Cast Stone</h1>
          <span>Interiors & Decorations</span>
        </div>
        <ul className={styles.mobileNavMenu}>
          <li>
            <div
              className={`${styles.mobileDropdownToggle} ${mobileDropdowns.company ? styles.active : ''}`}
              onClick={() => setMobileDropdowns(prev => ({...prev, company: !prev.company}))}
            >
              <span>Company</span>
            </div>
            <ul className={`${styles.mobileDropdownMenu} ${mobileDropdowns.company ? styles.active : ''}`}>
              <li><Link href="/contact" onClick={() => setMobileMenuOpen(false)}>Contact Us</Link></li>
              <li><Link href="/about" onClick={() => setMobileMenuOpen(false)}>Our Story</Link></li>
              <li><Link href="/retail-locator" onClick={() => setMobileMenuOpen(false)}>Retail Locator</Link></li>
              <li><Link href="/wholesale-signup" onClick={() => setMobileMenuOpen(false)}>Wholesale Sign-up</Link></li>
            </ul>
          </li>
          <li>
            <div
              className={`${styles.mobileDropdownToggle} ${mobileDropdowns.products ? styles.active : ''}`}
              onClick={() => setMobileDropdowns(prev => ({...prev, products: !prev.products}))}
            >
              <span>Products</span>
            </div>
            <ul className={`${styles.mobileDropdownMenu} ${mobileDropdowns.products ? styles.active : ''}`}>
              <li><Link href="/collections/architectural" onClick={() => setMobileMenuOpen(false)}>Architectural</Link></li>
              <li><Link href="/collections/designer" onClick={() => setMobileMenuOpen(false)}>Designer</Link></li>
              <li><Link href="/collections/limited-edition" onClick={() => setMobileMenuOpen(false)}>Limited Edition</Link></li>
              <li><Link href="/collections/cast-stone-sealers" onClick={() => setMobileMenuOpen(false)}>Cast Stone Sealers</Link></li>
              <li><Link href="/products" onClick={() => setMobileMenuOpen(false)}>All Products</Link></li>
            </ul>
          </li>
          <li>
            <Link href="/collections" onClick={() => setMobileMenuOpen(false)}>Collections</Link>
          </li>
          <li>
            <Link href="/projects" onClick={() => setMobileMenuOpen(false)}>Completed Projects</Link>
          </li>
          <li>
            <div
              className={`${styles.mobileDropdownToggle} ${mobileDropdowns.discover ? styles.active : ''}`}
              onClick={() => setMobileDropdowns(prev => ({...prev, discover: !prev.discover}))}
            >
              <span>Discover</span>
            </div>
            <ul className={`${styles.mobileDropdownMenu} ${mobileDropdowns.discover ? styles.active : ''}`}>
              <li><Link href="/catalog" onClick={() => setMobileMenuOpen(false)}>Catalog</Link></li>
              <li><Link href="/finishes" onClick={() => setMobileMenuOpen(false)}>Finishes</Link></li>
              <li><Link href="/videos" onClick={() => setMobileMenuOpen(false)}>Videos</Link></li>
              <li><Link href="/technical" onClick={() => setMobileMenuOpen(false)}>Technical Info</Link></li>
              <li><Link href="/faqs" onClick={() => setMobileMenuOpen(false)}>FAQs</Link></li>
            </ul>
          </li>
        </ul>
      </div>

      {/* Cart Sidebar */}
      <CartSidebar />
    </nav>
  );
}
