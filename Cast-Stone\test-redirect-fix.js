/**
 * Test Redirect Fix
 * Tests the login redirect issue fix
 */

const fetch = require('node-fetch');

async function testRedirectFix() {
  console.log('🔧 Testing Login Redirect Fix');
  console.log('==============================\n');
  
  try {
    // Step 1: Test that login page is accessible
    console.log('📍 Step 1: Test login page accessibility');
    const loginPageResponse = await fetch('http://localhost:3000/admin/login');
    console.log('Login page status:', loginPageResponse.status);
    
    if (loginPageResponse.status !== 200) {
      throw new Error('Login page not accessible');
    }
    console.log('✅ Login page accessible');
    
    // Step 2: Test that dashboard redirects to login when not authenticated
    console.log('\n📍 Step 2: Test dashboard redirect (unauthenticated)');
    const dashboardResponse = await fetch('http://localhost:3000/admin/dashboard', {
      redirect: 'manual'
    });
    
    console.log('Dashboard response status:', dashboardResponse.status);
    const location = dashboardResponse.headers.get('location');
    console.log('Redirect location:', location);
    
    if (dashboardResponse.status === 307 && location && location.includes('/admin/login')) {
      console.log('✅ Dashboard correctly redirects to login when unauthenticated');
    } else {
      console.log('⚠️  Unexpected dashboard behavior');
    }
    
    // Step 3: Test backend login API
    console.log('\n📍 Step 3: Test backend login API');
    const loginApiResponse = await fetch('http://localhost:5000/api/admin/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: '132Trent@!'
      })
    });
    
    const loginResult = await loginApiResponse.json();
    console.log('Login API status:', loginApiResponse.status);
    console.log('Login API success:', loginResult.success);
    
    if (loginResult.success) {
      console.log('✅ Backend login API working');
      console.log('Token received:', !!loginResult.token);
      console.log('Admin data received:', !!loginResult.admin);
    } else {
      throw new Error('Backend login failed: ' + loginResult.message);
    }
    
    // Step 4: Test token verification
    console.log('\n📍 Step 4: Test token verification');
    const verifyResponse = await fetch('http://localhost:5000/api/admin/verify-token', {
      headers: { 'Authorization': `Bearer ${loginResult.token}` }
    });
    
    const verifyResult = await verifyResponse.json();
    console.log('Token verification status:', verifyResponse.status);
    console.log('Token verification success:', verifyResult.success);
    
    if (verifyResult.success) {
      console.log('✅ Token verification working');
    } else {
      throw new Error('Token verification failed');
    }
    
    console.log('\n🎯 Analysis:');
    console.log('- Frontend login page: ✅ Working');
    console.log('- Dashboard protection: ✅ Working');
    console.log('- Backend login API: ✅ Working');
    console.log('- Token verification: ✅ Working');
    
    console.log('\n💡 The backend authentication is working correctly.');
    console.log('💡 The redirect issue is in the frontend state management.');
    console.log('💡 Try logging in manually and check browser console for errors.');
    
    console.log('\n🔍 Manual Test Instructions:');
    console.log('1. Open http://localhost:3000/admin/login in browser');
    console.log('2. Open browser DevTools (F12) and go to Console tab');
    console.log('3. Enter credentials: <EMAIL> / 132Trent@!');
    console.log('4. Click login and watch console logs');
    console.log('5. Check if you get redirected to dashboard or back to login');
    
    return true;
    
  } catch (error) {
    console.log('❌ Test failed:', error.message);
    return false;
  }
}

// Run test
testRedirectFix().then(success => {
  console.log('\n' + '='.repeat(50));
  console.log(success ? '✅ ANALYSIS COMPLETE' : '❌ ANALYSIS FAILED');
  console.log('='.repeat(50));
}).catch(error => {
  console.error('Test error:', error);
});
