.collectionsPage {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--color-border);
}

.title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.subtitle {
  font-size: 16px;
  color: #4a4a4a;
  margin: 0;
  font-weight: 500;
}

.headerActions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.viewToggle {
  display: flex;
  background: var(--color-gray-100);
  border-radius: 8px;
  padding: 4px;
}

.toggleButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: transparent;
  color: #2d2d2d;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.toggleButton.active {
  background: white;
  color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-weight: 700;
}

.toggleButton:hover:not(.active) {
  color: #1a1a1a;
  background: rgba(255, 255, 255, 0.5);
}

.primaryButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 24px;
  background: var(--color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.primaryButton:hover {
  background: var(--color-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.secondaryButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 18px;
  background: white;
  color: #2d2d2d;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.secondaryButton:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #1a1a1a;
}

/* Toolbar Styles */
.toolbar {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  padding: 20px;
  margin-bottom: 24px;
}

.searchSection {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.searchBox {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.searchBox svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-gray-400);
}

.searchInput {
  width: 100%;
  padding: 14px 14px 14px 44px;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 15px;
  background: white;
  transition: all 0.2s;
  color: #1a1a1a;
  font-weight: 500;
}

.searchInput:focus {
  outline: none;
  border-color: var(--color-primary);
  background: white;
  box-shadow: 0 0 0 3px var(--color-primary-light);
}

.searchInput::placeholder {
  color: #6b7280;
  font-weight: 400;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 14px 18px;
  background: white;
  color: #2d2d2d;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.filterButton:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  color: #1a1a1a;
}

.filterButton.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filterButton svg.rotated {
  transform: rotate(180deg);
}

.filtersPanel {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 0 0 0;
  border-top: 1px solid var(--color-border);
  flex-wrap: wrap;
}

.filterGroup {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filterGroup label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-700);
  white-space: nowrap;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: var(--color-gray-700);
  cursor: pointer;
}

.filterSelect:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px var(--color-primary-light);
}

.filterActions {
  margin-left: auto;
}

.clearFiltersButton {
  padding: 8px 16px;
  background: transparent;
  color: var(--color-gray-600);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.clearFiltersButton:hover {
  background: var(--color-gray-50);
  color: var(--color-gray-800);
}

.content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 32px;
  height: calc(100vh - 200px);
}

.sidebar {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.mainContent {
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.collectionDetails {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detailsHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-gray-50);
}

.collectionInfo h2 {
  font-size: 24px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 8px 0;
}

.collectionMeta {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.metaItem {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: var(--color-gray-600);
}

.statusBadge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.statusBadge.published {
  background: var(--color-success-light);
  color: var(--color-success-dark);
}

.statusBadge.draft {
  background: var(--color-warning-light);
  color: var(--color-warning-dark);
}

.detailsActions {
  display: flex;
  gap: 12px;
}

.detailsContent {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.descriptionSection,
.statsSection,
.childrenSection {
  margin-bottom: 32px;
}

.descriptionSection h3,
.statsSection h3,
.childrenSection h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0 0 16px 0;
}

.descriptionSection p {
  font-size: 14px;
  color: var(--color-gray-700);
  line-height: 1.6;
  margin: 0;
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.statCard {
  padding: 20px;
  background: var(--color-gray-50);
  border-radius: 8px;
  text-align: center;
}

.statValue {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-primary);
  margin-bottom: 4px;
}

.statLabel {
  font-size: 12px;
  color: var(--color-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.childrenGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.childCard {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--color-gray-50);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.childCard:hover {
  background: var(--color-gray-100);
  border-color: var(--color-primary);
}

.childIcon {
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
}

.childInfo {
  flex: 1;
}

.childInfo h4 {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-gray-900);
  margin: 0 0 4px 0;
}

.childInfo p {
  font-size: 12px;
  color: var(--color-gray-600);
  margin: 0;
}

.childStatus {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--color-gray-600);
  text-align: center;
  padding: 40px;
}

.emptyState svg {
  color: var(--color-gray-400);
  margin-bottom: 16px;
}

.emptyState h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-gray-700);
  margin: 0 0 8px 0;
}

.emptyState p {
  font-size: 14px;
  color: var(--color-gray-600);
  margin: 0;
  max-width: 400px;
}

/* List View Styles */
.listView {
  background: white;
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
}

.loadingState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--color-gray-600);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.collectionsTable {
  width: 100%;
}

.tableHeader {
  background: var(--color-gray-50);
  border-bottom: 1px solid var(--color-border);
}

.tableBody {
  background: white;
}

.tableRow {
  display: grid;
  grid-template-columns: 2fr 100px 100px 120px 140px;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--color-border);
  transition: background-color 0.2s;
}

.tableRow:last-child {
  border-bottom: none;
}

.tableBody .tableRow:hover {
  background: var(--color-gray-50);
}

.tableHeader .tableRow {
  font-weight: 700;
  color: #1a1a1a;
  font-size: 14px;
}

.tableCell {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #2d2d2d;
  font-weight: 500;
}

.collectionCell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collectionIcon {
  color: var(--color-gray-500);
  display: flex;
  align-items: center;
}

.collectionInfo h4 {
  font-size: 15px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 2px 0;
}

.collectionInfo p {
  font-size: 13px;
  color: #4a4a4a;
  margin: 0 0 4px 0;
  font-family: monospace;
  font-weight: 500;
}

.collectionInfo .description {
  font-size: 12px;
  color: var(--color-gray-600);
  font-style: italic;
  display: block;
  margin-top: 4px;
}

.typeBadge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.typeBadge.manual {
  background: var(--color-blue-light);
  color: var(--color-blue-dark);
}

.typeBadge.smart {
  background: var(--color-purple-light);
  color: var(--color-purple-dark);
}

.levelBadge {
  padding: 4px 8px;
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.productCount {
  font-weight: 600;
  color: var(--color-gray-900);
}

.actionButtons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  color: var(--color-gray-600);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.actionButton:hover {
  background: var(--color-gray-50);
  color: var(--color-gray-800);
  border-color: var(--color-gray-300);
}

.actionButton.deleteButton:hover {
  background: var(--color-error-light);
  color: var(--color-error);
  border-color: var(--color-error);
}

/* Responsive design */
@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .sidebar {
    order: 2;
  }

  .mainContent {
    order: 1;
  }

  .tableRow {
    grid-template-columns: 1fr 80px 80px 100px;
    gap: 8px;
  }

  .tableCell:nth-child(2) {
    display: none; /* Hide level on medium screens */
  }
}

@media (max-width: 768px) {
  .collectionsPage {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .headerActions {
    flex-direction: column;
    gap: 12px;
  }

  .viewToggle {
    align-self: stretch;
  }

  .title {
    font-size: 24px;
  }

  .content {
    height: auto;
    min-height: 600px;
  }

  .searchSection {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .searchBox {
    max-width: none;
  }

  .filtersPanel {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filterActions {
    margin-left: 0;
  }

  .tableRow {
    grid-template-columns: 1fr 60px;
    gap: 8px;
    padding: 12px 16px;
  }

  .tableCell:nth-child(2),
  .tableCell:nth-child(3),
  .tableCell:nth-child(4) {
    display: none; /* Hide level, products, status on mobile */
  }

  .collectionInfo .description {
    display: none; /* Hide description on mobile */
  }

  .actionButtons {
    flex-direction: column;
    gap: 4px;
  }

  .actionButton {
    width: 28px;
    height: 28px;
  }

  .detailsHeader {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .detailsActions {
    justify-content: stretch;
  }

  .detailsActions button {
    flex: 1;
  }

  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .childrenGrid {
    grid-template-columns: 1fr;
  }
}
