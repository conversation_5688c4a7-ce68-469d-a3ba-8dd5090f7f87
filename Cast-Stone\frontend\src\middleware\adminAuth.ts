/**
 * Admin Authentication Middleware
 * Protects admin routes and handles authentication redirects
 */

import { NextRequest, NextResponse } from 'next/server';

// Admin routes that require authentication
const PROTECTED_ADMIN_ROUTES = [
  '/admin/dashboard',
  '/admin/products',
  '/admin/orders',
  '/admin/users',
  '/admin/analytics',
  '/admin/settings',
  '/admin/admin-users',
  '/admin/collections'
];

// Public admin routes (no authentication required)
const PUBLIC_ADMIN_ROUTES = [
  '/admin/login'
];

// Check if token is expired
const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return Date.now() >= payload.exp * 1000;
  } catch {
    return true;
  }
};

export function adminAuthMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Only handle admin routes
  if (!pathname.startsWith('/admin')) {
    return NextResponse.next();
  }
  
  // Allow public admin routes
  if (PUBLIC_ADMIN_ROUTES.some(route => pathname.startsWith(route))) {
    return NextResponse.next();
  }
  
  // Check if route requires authentication
  const isProtectedRoute = PROTECTED_ADMIN_ROUTES.some(route => 
    pathname.startsWith(route)
  );
  
  if (!isProtectedRoute) {
    return NextResponse.next();
  }
  
  // Get token from cookies or headers
  const token = request.cookies.get('adminToken')?.value || 
                request.headers.get('authorization')?.replace('Bearer ', '');
  
  // Redirect to login if no token or token is expired
  if (!token || isTokenExpired(token)) {
    const loginUrl = new URL('/admin/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }
  
  // Token exists and is valid, allow access
  return NextResponse.next();
}
