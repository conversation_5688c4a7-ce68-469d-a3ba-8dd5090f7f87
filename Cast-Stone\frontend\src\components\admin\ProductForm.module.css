.productForm {
  background: var(--bg-primary);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  overflow: hidden;
  max-width: 1000px;
  margin: 0 auto;
}

.formHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.formHeader h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.formActions {
  display: flex;
  gap: 1rem;
}

.cancelButton,
.saveButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancelButton {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.cancelButton:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.saveButton {
  background: var(--admin-accent);
  color: white;
}

.saveButton:hover:not(:disabled) {
  background: var(--admin-accent-dark);
  transform: translateY(-1px);
}

.saveButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Tabs */
.formTabs {
  display: flex;
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  overflow-x: auto;
}

.tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  border-bottom: 3px solid transparent;
}

.tab:hover {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.tab.active {
  background: var(--bg-primary);
  color: var(--admin-accent);
  border-bottom-color: var(--admin-accent);
}

/* Form Content */
.form {
  padding: 2rem;
}

.tabContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
}

.section h3 {
  margin: 0 0 1.5rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-light);
  padding-bottom: 0.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup:last-child {
  margin-bottom: 0;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.formGroup small {
  display: block;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.input,
.textarea,
.select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.input:focus,
.textarea:focus,
.select:focus {
  outline: none;
  border-color: var(--admin-accent);
  box-shadow: 0 0 0 3px var(--primary-color-10);
}

.textarea {
  resize: vertical;
  min-height: 100px;
}

/* Tags */
.tagsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tagsList {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--admin-accent-light);
  color: var(--admin-accent-dark);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.tagRemove {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  transition: background-color 0.2s ease;
}

.tagRemove:hover {
  background: rgba(255, 255, 255, 0.2);
}

.addTag {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.addButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background: var(--admin-accent);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.addButton:hover {
  background: var(--admin-accent-dark);
}

/* Variants */
.variantCard {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.variantHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.variantHeader h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.removeButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  background: var(--error-light);
  color: var(--error-dark);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.removeButton:hover {
  background: var(--error-color);
  color: white;
}

.dimensionsGroup {
  margin-bottom: 1rem;
}

.dimensionsGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.dimensionsRow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dimensionsRow span {
  color: var(--text-secondary);
  font-weight: 500;
}

.addVariantButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.addVariantButton:hover {
  background: var(--bg-tertiary);
  border-color: var(--admin-accent);
  color: var(--admin-accent);
}

/* Media Upload */
.mediaUpload {
  margin-bottom: 2rem;
}

.uploadArea {
  border: 2px dashed var(--border-color);
  border-radius: 8px;
  padding: 3rem;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
}

.uploadArea:hover {
  border-color: var(--admin-accent);
  background: var(--bg-secondary);
}

.uploadArea svg {
  color: var(--text-muted);
  margin-bottom: 1rem;
}

.uploadArea p {
  margin: 0;
  color: var(--text-secondary);
}

.imagesList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.imageItem {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
}

.imageItem img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.imageActions {
  padding: 1rem;
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.imageActions .input {
  flex: 1;
  margin: 0;
}

/* Responsive */
@media (max-width: 768px) {
  .formHeader {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .formActions {
    justify-content: stretch;
  }
  
  .formActions button {
    flex: 1;
  }
  
  .formTabs {
    flex-direction: column;
  }
  
  .form {
    padding: 1rem;
  }
  
  .formRow {
    grid-template-columns: 1fr;
  }
  
  .dimensionsRow {
    flex-direction: column;
    align-items: stretch;
  }
  
  .dimensionsRow span {
    display: none;
  }
  
  .imagesList {
    grid-template-columns: 1fr;
  }
}
