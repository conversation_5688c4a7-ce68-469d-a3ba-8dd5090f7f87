'use client';

import { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  FolderTree,
  Package,
  Eye,
  Settings,
  Grid,
  List,
  MoreVertical,
  ChevronDown
} from 'lucide-react';
import { useAdmin } from '../../../contexts/AdminContext';
import CollectionHierarchy from '../../../components/admin/CollectionHierarchy';
import CollectionModal from '../../../components/admin/CollectionModal';
import { collectionsApi } from '../../../services/api';
import styles from './page.module.css';

interface Collection {
  _id: string;
  title: string;
  handle: string;
  description: string;
  level: number;
  path: string;
  parent?: {
    _id: string;
    title: string;
    handle: string;
    level: number;
  } | string;
  children: Array<{
    _id: string;
    title: string;
    handle: string;
    level: number;
  }> | string[];
  published: boolean;
  productCount?: number;
  createdAt: string;
  updatedAt: string;
}

export default function CollectionsManagement() {
  const { hasPermission, addNotification } = useAdmin();
  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [parentCollection, setParentCollection] = useState<Collection | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // New state for improved UI
  const [viewMode, setViewMode] = useState<'hierarchy' | 'list'>('hierarchy');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'published' | 'draft'>('all');
  const [typeFilter, setTypeFilter] = useState<'all'>('all');
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    fetchCollections();
  }, [refreshKey]);

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm !== '') {
        fetchCollections();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const fetchCollections = async () => {
    try {
      setIsLoading(true);
      // Use admin API to get all collections including unpublished ones
      const response = await collectionsApi.getAllCollections({
        limit: 100,
        search: searchTerm || undefined
      });
      setCollections(response.collections || []);
    } catch (error) {
      console.error('Failed to fetch collections:', error);
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load collections. Please check your permissions.'
      });
      // Fallback to public API if admin API fails
      try {
        const fallbackResponse = await collectionsApi.getCollections({ limit: 100 });
        setCollections(fallbackResponse.collections || []);
      } catch (fallbackError) {
        console.error('Fallback API also failed:', fallbackError);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const filteredCollections = collections.filter(collection => {
    const matchesSearch = collection.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         collection.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         collection.handle.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' ||
                         (statusFilter === 'published' && collection.published) ||
                         (statusFilter === 'draft' && !collection.published);

    const matchesType = typeFilter === 'all';

    return matchesSearch && matchesStatus && matchesType;
  });

  const handleSelectCollection = (collection: Collection) => {
    setSelectedCollection(collection);
  };

  const handleEditCollection = (collection: Collection) => {
    setSelectedCollection(collection);
    setModalMode('edit');
    setShowCollectionModal(true);
  };

  const handleDeleteCollection = async (collection: Collection) => {
    if (!hasPermission('products', 'delete')) {
      addNotification({
        type: 'error',
        title: 'Permission Denied',
        message: 'You do not have permission to delete collections'
      });
      return;
    }

    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${collection.title}"? This action cannot be undone.`
    );

    if (!confirmDelete) return;

    try {
      setIsLoading(true);
      await collectionsApi.deleteCollection(collection._id);

      addNotification({
        type: 'success',
        title: 'Collection Deleted',
        message: `"${collection.title}" has been deleted successfully`
      });

      setRefreshKey(prev => prev + 1);
      if (selectedCollection?._id === collection._id) {
        setSelectedCollection(null);
      }
    } catch (error) {
      console.error('Failed to delete collection:', error);
      addNotification({
        type: 'error',
        title: 'Delete Failed',
        message: 'Failed to delete collection. Please try again.'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddSubCollection = (parent: Collection | null) => {
    setParentCollection(parent);
    setSelectedCollection(null);
    setModalMode('create');
    setShowCollectionModal(true);
  };

  const handleSaveCollection = async (collectionData: any) => {
    try {
      setIsLoading(true);

      // Validate required permissions
      const requiredPermission = modalMode === 'create' ? 'create' : 'update';
      if (!hasPermission('products', requiredPermission)) {
        throw new Error(`You do not have permission to ${modalMode} collections`);
      }

      let result;
      if (modalMode === 'create') {
        result = await collectionsApi.createCollection(collectionData);
        addNotification({
          type: 'success',
          title: 'Collection Created',
          message: `"${collectionData.title}" has been created successfully`
        });
      } else {
        result = await collectionsApi.updateCollection(selectedCollection!._id, collectionData);
        addNotification({
          type: 'success',
          title: 'Collection Updated',
          message: `"${collectionData.title}" has been updated successfully`
        });
      }

      setRefreshKey(prev => prev + 1);
      setShowCollectionModal(false);

      // Update selected collection if we're editing
      if (modalMode === 'edit' && result?.collection) {
        setSelectedCollection(result.collection);
      }
    } catch (error: any) {
      console.error('Failed to save collection:', error);

      // Add user-friendly error notification
      addNotification({
        type: 'error',
        title: `Failed to ${modalMode} Collection`,
        message: error.message || `An error occurred while ${modalMode === 'create' ? 'creating' : 'updating'} the collection`
      });

      throw error; // Re-throw to let modal handle the error
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseModal = () => {
    setShowCollectionModal(false);
    setSelectedCollection(null);
    setParentCollection(null);
  };

  return (
    <div className={styles.collectionsPage}>
      <div className={styles.header}>
        <div>
          <h1 className={styles.title}>Collections Management</h1>
          <p className={styles.subtitle}>
            Manage your hierarchical collection structure and organize products
          </p>
        </div>

        <div className={styles.headerActions}>
          <div className={styles.viewToggle}>
            <button
              className={`${styles.toggleButton} ${viewMode === 'hierarchy' ? styles.active : ''}`}
              onClick={() => setViewMode('hierarchy')}
            >
              <FolderTree size={16} />
              Hierarchy
            </button>
            <button
              className={`${styles.toggleButton} ${viewMode === 'list' ? styles.active : ''}`}
              onClick={() => setViewMode('list')}
            >
              <List size={16} />
              List
            </button>
          </div>

          {hasPermission('products', 'create') && (
            <button
              className={styles.primaryButton}
              onClick={() => handleAddSubCollection(null)}
            >
              <Plus />
              Add Collection
            </button>
          )}
        </div>
      </div>

      {/* Search and Filters */}
      <div className={styles.toolbar}>
        <div className={styles.searchSection}>
          <div className={styles.searchBox}>
            <Search size={20} />
            <input
              type="text"
              placeholder="Search collections..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>

          <button
            className={`${styles.filterButton} ${showFilters ? styles.active : ''}`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={16} />
            Filters
            <ChevronDown size={16} className={showFilters ? styles.rotated : ''} />
          </button>
        </div>

        {showFilters && (
          <div className={styles.filtersPanel}>
            <div className={styles.filterGroup}>
              <label>Status:</label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value as any)}
                className={styles.filterSelect}
              >
                <option value="all">All Status</option>
                <option value="published">Published</option>
                <option value="draft">Draft</option>
              </select>
            </div>



            <div className={styles.filterActions}>
              <button
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                }}
                className={styles.clearFiltersButton}
              >
                Clear All
              </button>
            </div>
          </div>
        )}
      </div>

      <div className={styles.content}>
        {viewMode === 'hierarchy' ? (
          <>
            <div className={styles.sidebar}>
              <CollectionHierarchy
                key={refreshKey}
                onSelectCollection={handleSelectCollection}
                onEditCollection={handleEditCollection}
                onDeleteCollection={handleDeleteCollection}
                onAddSubCollection={handleAddSubCollection}
                selectedCollectionId={selectedCollection?._id}
              />
            </div>

            <div className={styles.mainContent}>
              {selectedCollection ? (
                <div className={styles.collectionDetails}>
                  <div className={styles.detailsHeader}>
                    <div className={styles.collectionInfo}>
                      <h2>{selectedCollection.title}</h2>
                      <div className={styles.collectionMeta}>
                        <span className={styles.metaItem}>
                          <Package size={16} />
                          Level {selectedCollection.level}
                        </span>
                        <span className={styles.metaItem}>
                          <FolderTree size={16} />
                          {selectedCollection.path}
                        </span>
                        <span className={`${styles.statusBadge} ${selectedCollection.published ? styles.published : styles.draft}`}>
                          {selectedCollection.published ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    </div>

                    <div className={styles.detailsActions}>
                      {hasPermission('products', 'update') && (
                        <button
                          onClick={() => handleEditCollection(selectedCollection)}
                          className={styles.secondaryButton}
                        >
                          <Edit size={16} />
                          Edit
                        </button>
                      )}
                      <button className={styles.secondaryButton}>
                        <Eye size={16} />
                        View Products
                      </button>
                    </div>
                  </div>

                  <div className={styles.detailsContent}>
                    <div className={styles.descriptionSection}>
                      <h3>Description</h3>
                      <p>{selectedCollection.description || 'No description provided.'}</p>
                    </div>

                    <div className={styles.statsSection}>
                      <h3>Statistics</h3>
                      <div className={styles.statsGrid}>
                        <div className={styles.statCard}>
                          <div className={styles.statValue}>
                            {selectedCollection.productCount || 0}
                          </div>
                          <div className={styles.statLabel}>Products</div>
                        </div>
                        <div className={styles.statCard}>
                          <div className={styles.statValue}>
                            {selectedCollection.children?.length || 0}
                          </div>
                          <div className={styles.statLabel}>Sub Collections</div>
                        </div>
                        <div className={styles.statCard}>
                          <div className={styles.statValue}>
                            {selectedCollection.collectionType}
                          </div>
                          <div className={styles.statLabel}>Type</div>
                        </div>
                      </div>
                    </div>

                    {selectedCollection.children && selectedCollection.children.length > 0 && (
                      <div className={styles.childrenSection}>
                        <h3>Sub Collections</h3>
                        <div className={styles.childrenGrid}>
                          {selectedCollection.children.map((child) => (
                            <div
                              key={child._id}
                              className={styles.childCard}
                              onClick={() => setSelectedCollection(child)}
                            >
                              <div className={styles.childIcon}>
                                <Package size={20} />
                              </div>
                              <div className={styles.childInfo}>
                                <h4>{child.title}</h4>
                                <p>Level {child.level} • {child.handle}</p>
                              </div>
                              <div className={`${styles.childStatus} ${child.published ? styles.published : styles.draft}`}>
                                {child.published ? 'Published' : 'Draft'}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className={styles.emptyState}>
                  <FolderTree size={64} />
                  <h3>Select a Collection</h3>
                  <p>Choose a collection from the hierarchy to view its details and manage its settings.</p>
                </div>
              )}
            </div>
          </>
        ) : (
          <div className={styles.listView}>
            {isLoading ? (
              <div className={styles.loadingState}>
                <div className={styles.spinner}></div>
                <p>Loading collections...</p>
              </div>
            ) : filteredCollections.length === 0 ? (
              <div className={styles.emptyState}>
                <Package size={64} />
                <h3>No Collections Found</h3>
                <p>
                  {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                    ? 'No collections match your current filters.'
                    : 'Get started by creating your first collection.'}
                </p>
                {hasPermission('products', 'create') && (
                  <button
                    className={styles.primaryButton}
                    onClick={() => handleAddSubCollection(null)}
                  >
                    <Plus />
                    Create Collection
                  </button>
                )}
              </div>
            ) : (
              <div className={styles.collectionsTable}>
                <div className={styles.tableHeader}>
                  <div className={styles.tableRow}>
                    <div className={styles.tableCell}>Collection</div>
                    <div className={styles.tableCell}>Level</div>
                    <div className={styles.tableCell}>Parent</div>
                    <div className={styles.tableCell}>Children</div>
                    <div className={styles.tableCell}>Products</div>
                    <div className={styles.tableCell}>Status</div>
                    <div className={styles.tableCell}>Actions</div>
                  </div>
                </div>

                <div className={styles.tableBody}>
                  {filteredCollections.map((collection) => (
                    <div key={collection._id} className={styles.tableRow}>
                      <div className={styles.tableCell}>
                        <div className={styles.collectionCell}>
                          <div className={styles.collectionIcon}>
                            <Package size={20} />
                          </div>
                          <div className={styles.collectionInfo}>
                            <h4>{collection.title}</h4>
                            <p>{collection.handle}</p>
                            {collection.description && (
                              <span className={styles.description}>
                                {collection.description.length > 60
                                  ? `${collection.description.substring(0, 60)}...`
                                  : collection.description}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className={styles.tableCell}>
                        <span className={styles.levelBadge}>
                          Level {collection.level}
                        </span>
                      </div>

                      <div className={styles.tableCell}>
                        {collection.parent && typeof collection.parent === 'object' ? (
                          <span className={styles.parentInfo}>
                            {collection.parent.title}
                          </span>
                        ) : (
                          <span className={styles.noParent}>Root</span>
                        )}
                      </div>

                      <div className={styles.tableCell}>
                        {collection.children && Array.isArray(collection.children) && collection.children.length > 0 ? (
                          <span className={styles.childrenCount}>
                            {typeof collection.children[0] === 'object'
                              ? collection.children.length
                              : collection.children.length} child{collection.children.length !== 1 ? 'ren' : ''}
                          </span>
                        ) : (
                          <span className={styles.noChildren}>None</span>
                        )}
                      </div>

                      <div className={styles.tableCell}>
                        <span className={styles.productCount}>
                          {collection.productCount || 0}
                        </span>
                      </div>

                      <div className={styles.tableCell}>
                        <span className={`${styles.statusBadge} ${collection.published ? styles.published : styles.draft}`}>
                          {collection.published ? 'Published' : 'Draft'}
                        </span>
                      </div>

                      <div className={styles.tableCell}>
                        <div className={styles.actionButtons}>
                          {hasPermission('products', 'read') && (
                            <button
                              onClick={() => handleSelectCollection(collection)}
                              className={styles.actionButton}
                              title="View Details"
                            >
                              <Eye size={16} />
                            </button>
                          )}
                          {hasPermission('products', 'update') && (
                            <button
                              onClick={() => handleEditCollection(collection)}
                              className={styles.actionButton}
                              title="Edit Collection"
                            >
                              <Edit size={16} />
                            </button>
                          )}
                          {hasPermission('products', 'delete') && (
                            <button
                              onClick={() => handleDeleteCollection(collection)}
                              className={`${styles.actionButton} ${styles.deleteButton}`}
                              title="Delete Collection"
                            >
                              <Trash2 size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Collection Modal */}
      {showCollectionModal && (
        <CollectionModal
          isOpen={showCollectionModal}
          onClose={handleCloseModal}
          onSave={handleSaveCollection}
          collection={modalMode === 'edit' ? selectedCollection : null}
          parentCollection={parentCollection}
          mode={modalMode}
        />
      )}
    </div>
  );
}
