#!/usr/bin/env node

/**
 * Comprehensive Admin Login Test Script
 * Tests all login scenarios including success, failure, and edge cases
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = 'http://localhost:5000/api';

// Test scenarios
const testScenarios = [
  {
    name: 'Valid Admin Login',
    credentials: {
      email: 'mum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com',
      password: '132Trent@!'
    },
    expectedStatus: 200,
    expectedSuccess: true
  },
  {
    name: 'Invalid Email',
    credentials: {
      email: '<EMAIL>',
      password: '132Trent@!'
    },
    expectedStatus: 401,
    expectedSuccess: false
  },
  {
    name: 'Invalid Password',
    credentials: {
      email: '<EMAIL>',
      password: 'wrongpassword'
    },
    expectedStatus: 401,
    expectedSuccess: false
  },
  {
    name: 'Missing Email',
    credentials: {
      password: '132Trent@!'
    },
    expectedStatus: 400,
    expectedSuccess: false
  },
  {
    name: 'Missing Password',
    credentials: {
      email: 'mum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com'
    },
    expectedStatus: 400,
    expectedSuccess: false
  },
  {
    name: 'Empty Credentials',
    credentials: {},
    expectedStatus: 400,
    expectedSuccess: false
  },
  {
    name: 'Malformed Email',
    credentials: {
      email: 'not-an-email',
      password: '132Trent@!'
    },
    expectedStatus: 401,
    expectedSuccess: false
  }
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  title: (msg) => console.log(`${colors.bold}${colors.blue}${msg}${colors.reset}`)
};

// Test function
async function testLogin(scenario) {
  try {
    const response = await axios.post(`${API_BASE_URL}/admin/login`, scenario.credentials, {
      validateStatus: () => true // Don't throw on non-2xx status codes
    });

    const { status, data } = response;
    
    // Check status code
    const statusMatch = status === scenario.expectedStatus;
    
    // Check success field
    const successMatch = data.success === scenario.expectedSuccess;
    
    // Check required fields for successful login
    let fieldsValid = true;
    if (scenario.expectedSuccess) {
      fieldsValid = !!(data.token && data.admin && data.admin.email && data.admin.role);
    }
    
    const passed = statusMatch && successMatch && fieldsValid;
    
    if (passed) {
      log.success(`${scenario.name}: PASSED`);
      if (scenario.expectedSuccess) {
        log.info(`  Token: ${data.token.substring(0, 20)}...`);
        log.info(`  Admin: ${data.admin.email} (${data.admin.role})`);
      }
    } else {
      log.error(`${scenario.name}: FAILED`);
      if (!statusMatch) {
        log.error(`  Expected status: ${scenario.expectedStatus}, got: ${status}`);
      }
      if (!successMatch) {
        log.error(`  Expected success: ${scenario.expectedSuccess}, got: ${data.success}`);
      }
      if (!fieldsValid) {
        log.error(`  Missing required fields in response`);
      }
      log.error(`  Response: ${JSON.stringify(data, null, 2)}`);
    }
    
    return passed;
  } catch (error) {
    log.error(`${scenario.name}: ERROR - ${error.message}`);
    return false;
  }
}

// Test token verification
async function testTokenVerification(token) {
  try {
    const response = await axios.get(`${API_BASE_URL}/admin/verify-token`, {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      validateStatus: () => true
    });
    
    if (response.status === 200 && response.data.success) {
      log.success('Token Verification: PASSED');
      return true;
    } else {
      log.error('Token Verification: FAILED');
      log.error(`  Response: ${JSON.stringify(response.data, null, 2)}`);
      return false;
    }
  } catch (error) {
    log.error(`Token Verification: ERROR - ${error.message}`);
    return false;
  }
}

// Main test function
async function runTests() {
  log.title('🚀 Admin Login Test Suite');
  log.title('================================\n');
  
  let passedTests = 0;
  let totalTests = testScenarios.length;
  let validToken = null;
  
  // Run all test scenarios
  for (const scenario of testScenarios) {
    const passed = await testLogin(scenario);
    if (passed) {
      passedTests++;
      
      // Store valid token for verification test
      if (scenario.expectedSuccess && !validToken) {
        const response = await axios.post(`${API_BASE_URL}/admin/login`, scenario.credentials);
        validToken = response.data.token;
      }
    }
    console.log(''); // Add spacing
  }
  
  // Test token verification if we have a valid token
  if (validToken) {
    const verificationPassed = await testTokenVerification(validToken);
    if (verificationPassed) {
      passedTests++;
    }
    totalTests++;
  }
  
  // Summary
  log.title('\n📊 Test Results');
  log.title('================');
  
  if (passedTests === totalTests) {
    log.success(`All tests passed! (${passedTests}/${totalTests})`);
    log.success('🎉 Admin login system is working perfectly!');
  } else {
    log.error(`${totalTests - passedTests} tests failed. (${passedTests}/${totalTests} passed)`);
    log.warning('Please review the failed tests and fix the issues.');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Handle process termination
process.on('SIGINT', () => {
  log.warning('\nTest suite interrupted by user.');
  process.exit(1);
});

// Run the tests
runTests().catch(error => {
  log.error(`Test suite failed: ${error.message}`);
  process.exit(1);
});
